package org.jeecg.modules.api.user_learn_log.service.impl;

import org.jeecg.modules.api.user_learn_log.entity.InzUserLearnLog;
import org.jeecg.modules.api.user_learn_log.mapper.InzUserLearnLogMapper;
import org.jeecg.modules.api.user_learn_log.service.IInzUserLearnLogService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户学习记录
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Service
public class InzUserLearnLogServiceImpl extends ServiceImpl<InzUserLearnLogMapper, InzUserLearnLog> implements IInzUserLearnLogService {

}
