<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.learning_module.mapper.InzLearningModuleMapper">

    <!-- 根据状态查询启用的模块列表 -->
    <select id="getModulesByStatus" resultType="org.jeecg.modules.api.learning_module.entity.InzLearningModule">
        SELECT * FROM inz_learning_module 
        WHERE status = #{status} 
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据年龄范围查询适合的模块 -->
    <select id="getModulesByAge" resultType="org.jeecg.modules.api.learning_module.entity.InzLearningModule">
        SELECT * FROM inz_learning_module 
        WHERE status = 1 
        AND (
            (target_age_min IS NULL OR target_age_min &lt;= #{age})
            AND (target_age_max IS NULL OR target_age_max &gt;= #{age})
        )
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据难度等级查询模块 -->
    <select id="getModulesByDifficulty" resultType="org.jeecg.modules.api.learning_module.entity.InzLearningModule">
        SELECT * FROM inz_learning_module 
        WHERE status = 1 
        AND difficulty_level = #{difficultyLevel}
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 更新模块统计信息 -->
    <update id="updateModuleStats">
        UPDATE inz_learning_module 
        SET total_videos = #{totalVideos},
            total_duration = #{totalDuration},
            update_time = NOW()
        WHERE id = #{moduleId}
    </update>

    <!-- 增加观看次数 -->
    <update id="incrementViewCount">
        UPDATE inz_learning_module 
        SET view_count = view_count + 1,
            update_time = NOW()
        WHERE id = #{moduleId}
    </update>

</mapper>
