package org.jeecg.modules.api.test_question.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.test_question.entity.InzTestQuestion;
import org.jeecg.modules.api.test_question.entity.InzWordQuestion;

import java.util.List;

/**
 * @Description: 存储固定搭配
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
public interface IInzWordQuestionService extends IService<InzWordQuestion> {

}
