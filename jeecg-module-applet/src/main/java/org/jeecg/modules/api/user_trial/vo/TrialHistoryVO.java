package org.jeecg.modules.api.user_trial.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 试用历史记录VO
 * @Author: Alex (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "试用历史记录", description = "用户试用历史记录信息")
public class TrialHistoryVO {

    /**记录ID*/
    @ApiModelProperty(value = "记录ID")
    private String id;

    /**试用天数*/
    @ApiModelProperty(value = "试用天数", example = "3")
    private Integer trialDays;

    /**试用开始日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "试用开始日期", example = "2025-07-30")
    private Date startDate;

    /**试用结束日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "试用结束日期", example = "2025-08-02")
    private Date endDate;

    /**状态*/
    @ApiModelProperty(value = "状态", example = "1")
    private Integer status;

    /**状态描述*/
    @ApiModelProperty(value = "状态描述", example = "有效")
    private String statusText;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**备注*/
    @ApiModelProperty(value = "备注", example = "首次试用")
    private String remark;

    /**是否当前试用*/
    @ApiModelProperty(value = "是否当前试用", example = "true")
    private Boolean isCurrent;
}
