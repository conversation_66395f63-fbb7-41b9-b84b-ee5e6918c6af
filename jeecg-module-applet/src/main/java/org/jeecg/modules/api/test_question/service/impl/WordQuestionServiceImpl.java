package org.jeecg.modules.api.test_question.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.test_question.entity.InzWordQuestion;
import org.jeecg.modules.api.test_question.mapper.WordsQuestionMapper;
import org.jeecg.modules.api.test_question.service.IInzWordQuestionService;
import org.springframework.stereotype.Service;

/**
 * @Description: 存储词源信息
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
@Service
public class WordQuestionServiceImpl extends ServiceImpl<WordsQuestionMapper, InzWordQuestion> implements IInzWordQuestionService {
	

}
