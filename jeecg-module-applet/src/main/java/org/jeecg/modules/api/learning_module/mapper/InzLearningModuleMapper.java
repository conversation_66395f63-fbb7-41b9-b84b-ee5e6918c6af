package org.jeecg.modules.api.learning_module.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.learning_module.entity.InzLearningModule;

import java.util.List;

/**
 * @Description: 学习模块表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface InzLearningModuleMapper extends BaseMapper<InzLearningModule> {

    /**
     * 根据状态查询启用的模块列表
     * @param status 状态
     * @return 模块列表
     */
    List<InzLearningModule> getModulesByStatus(@Param("status") Integer status);

    /**
     * 根据年龄范围查询适合的模块
     * @param age 年龄
     * @return 模块列表
     */
    List<InzLearningModule> getModulesByAge(@Param("age") Integer age);

    /**
     * 根据难度等级查询模块
     * @param difficultyLevel 难度等级
     * @return 模块列表
     */
    List<InzLearningModule> getModulesByDifficulty(@Param("difficultyLevel") Integer difficultyLevel);

    /**
     * 更新模块统计信息
     * @param moduleId 模块ID
     * @param totalVideos 总视频数
     * @param totalDuration 总时长
     */
    void updateModuleStats(@Param("moduleId") String moduleId, 
                          @Param("totalVideos") Integer totalVideos, 
                          @Param("totalDuration") Integer totalDuration);

    /**
     * 增加观看次数
     * @param moduleId 模块ID
     */
    void incrementViewCount(@Param("moduleId") String moduleId);
}
