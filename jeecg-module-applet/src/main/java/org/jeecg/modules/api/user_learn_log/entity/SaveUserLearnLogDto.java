package org.jeecg.modules.api.user_learn_log.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description: 用户学习记录
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_user_learn_log对象", description="用户学习记录")
public class SaveUserLearnLogDto implements Serializable {

	/**单词id*/
    @NotEmpty(message = "请先选择单词")
    @ApiModelProperty(value = "单词id",required = true)
    private String wordId;

    @NotEmpty(message = "请先选择词书")
    @ApiModelProperty(value = "词书id",required = true)
    private String bookId;

    @ApiModelProperty(value = "章节id")
    private String chapterId;

    @NotEmpty(message = "请选择分类")
    @ApiModelProperty(value = "0未归类  1生词 2学习中 3掌握",required = true)
    private String status;

    @ApiModelProperty(value = "学习时间")
    private String date;

}
