package org.jeecg.modules.api.user_front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.QrCodeUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.api.books.entity.WordBooks;
import org.jeecg.modules.api.books.service.WordBooksService;
import org.jeecg.modules.api.train_plan.entity.InzTrainPlan;
import org.jeecg.modules.api.train_plan.service.IInzTrainPlanService;
import org.jeecg.modules.api.user_books.entity.InzUserBooks;
import org.jeecg.modules.api.user_books.service.UserBooksService;
import org.jeecg.modules.api.user_collection.entity.InzUserCollection;
import org.jeecg.modules.api.user_collection.service.IInzUserCollectionService;
import org.jeecg.modules.api.user_collection_unit.entity.InzUserCollectionUnit;
import org.jeecg.modules.api.user_collection_unit.service.IInzUserCollectionUnitService;
import org.jeecg.modules.api.user_front.entity.UserFront;
import org.jeecg.modules.api.user_front.mapper.UserFrontMapper;
import org.jeecg.modules.api.user_front.service.UserFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @Description: inz_user_front
 * @Author: jeecg-boot
 * @Date:   2025-01-13
 * @Version: V1.0
 */
@Service
@Slf4j
public class UserFrontServiceImpl extends ServiceImpl<UserFrontMapper, UserFront> implements UserFrontService {

    @Autowired
    private IInzUserCollectionService iInzUserCollectionService;

    @Autowired
    private IInzUserCollectionUnitService iInzUserCollectionUnitService;

    @Autowired
    private UserBooksService userBooksService;

    @Autowired
    private WordBooksService wordBooksService;

    @Autowired
    private IInzTrainPlanService iInzTrainPlanService;

    @Autowired
    private RedisUtil redisUtil;

    @Value(value = "${jeecg.path.domain}")
    private String domain;

    @Override
    public boolean register(UserFront userFront) {
        // 生成带有注册页面URL和邀请码的二维码内容
        String registerUrl = domain + "/#/pages/login/sign-in/index?name=%E8%B4%A6%E5%8F%B7%E6%B3%A8%E5%86%8C&inviteCode=" + userFront.getInviteCode();

        String path;
        try {
            // 生成包含完整注册URL的二维码
            path = QrCodeUtil.generateQRCode(registerUrl, 200, 200);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        userFront.setInviteQrcodePath(path);

        // 从Redis配置中获取免费会员天数
        Object freeDays = redisUtil.get("SuperWords:config:FREE_MEMBERSHIP_DAYS");
        Integer freeDaysValue = 0;

        if (freeDays != null) {
            try {
                freeDaysValue = Integer.parseInt(freeDays.toString());
            } catch (NumberFormatException e) {
                log.warn("免费会员天数配置格式错误: {}, 使用默认值0", freeDays);
                freeDaysValue = 0;
            }
        } else {
            log.warn("免费会员天数配置为空，使用默认值0天");
        }

        log.info("用户注册 - 获取免费会员天数: {} 天", freeDaysValue);
        
        // 计算VIP到期时间 (天数 * 24小时 * 60分钟 * 60秒 * 1000毫秒)
        userFront.setVipTime(new Date(new Date().getTime() + freeDaysValue * 24L * 60L * 60L * 1000L));

        // 设置剩余试用天数为配置项中的免费会员天数
        userFront.setTrialRemainingDays(freeDaysValue);
        log.info("用户注册 - 设置剩余试用天数: {} 天", freeDaysValue);

        boolean saveResult = save(userFront);
        InzUserCollection inzUserCollection = new InzUserCollection();
        inzUserCollection.setName("单词本");
        inzUserCollection.setCreateBy(userFront.getId());
        inzUserCollection.setIsDefault(1);
        iInzUserCollectionService.save(inzUserCollection);

        InzUserCollectionUnit inzUserCollectionUnit = new InzUserCollectionUnit();
        inzUserCollectionUnit.setCollectionId(inzUserCollection.getId());
        inzUserCollectionUnit.setUnitName("Unit1");
        inzUserCollectionUnit.setCreateBy(userFront.getId());
        iInzUserCollectionUnitService.save(inzUserCollectionUnit);

        //找到填写年级的图书
        WordBooks books = wordBooksService.getOne(new QueryWrapper<WordBooks>().lambda().like(WordBooks::getName, userFront.getGrade()));
        if(books == null){
            List<WordBooks> list = wordBooksService.list(new QueryWrapper<WordBooks>().lambda().orderBy(true, true, WordBooks::getCreateTime));
            books = list.get(0);
        }

        InzUserBooks inzUserBooks = new InzUserBooks();
        inzUserBooks.setWordBookId(books.getId());
        inzUserBooks.setCreateBy(userFront.getId());
        userBooksService.save(inzUserBooks);

        //默认将图书添加到训练计划中
        InzTrainPlan inzTrainPlan = new InzTrainPlan();
        inzTrainPlan.setBookId(books.getId());
        inzTrainPlan.setCreateBy(userFront.getId());
        inzTrainPlan.setDailyWordsCount(0);
        inzTrainPlan.setFinishDays(0);
        iInzTrainPlanService.save(inzTrainPlan);

        return saveResult;
    }
}
