package org.jeecg.modules.api.evaluation_info.service.impl;

import org.jeecg.modules.api.evaluation_info.entity.InzEvaluationInfo;
import org.jeecg.modules.api.evaluation_info.mapper.InzEvaluationInfoMapper;
import org.jeecg.modules.api.evaluation_info.service.IInzEvaluationInfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 测评详情
 * @Author: jeecg-boot
 * @Date:   2025-01-24
 * @Version: V1.0
 */
@Service
public class InzEvaluationInfoServiceImpl extends ServiceImpl<InzEvaluationInfoMapper, InzEvaluationInfo> implements IInzEvaluationInfoService {

}
