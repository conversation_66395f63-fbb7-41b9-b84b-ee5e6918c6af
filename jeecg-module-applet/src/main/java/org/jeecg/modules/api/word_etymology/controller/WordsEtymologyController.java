package org.jeecg.modules.api.word_etymology.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.api.word_etymology.entity.WordsEtymology;
import org.jeecg.modules.api.word_etymology.service.WordsEtymologyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

 /**
 * @Description: 存储词源信息
 * @Author: jeecg-boot
 * @Date:   2025-01-16
 * @Version: V1.0
 */
@Api(tags="存储词源信息")
@RestController
@RequestMapping("/word_etymology")
@Slf4j
public class WordsEtymologyController extends JeecgController<WordsEtymology, WordsEtymologyService> {
	@Autowired
	private WordsEtymologyService inzWordsEtymologyService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzWordsEtymology
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "存储词源信息-分页列表查询")
	@ApiOperation(value="存储词源信息-分页列表查询", notes="存储词源信息-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<WordsEtymology>> queryPageList(WordsEtymology inzWordsEtymology,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<WordsEtymology> queryWrapper = QueryGenerator.initQueryWrapper(inzWordsEtymology, req.getParameterMap());
		Page<WordsEtymology> page = new Page<WordsEtymology>(pageNo, pageSize);
		IPage<WordsEtymology> pageList = inzWordsEtymologyService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "存储词源信息-通过id查询")
	@ApiOperation(value="存储词源信息-通过id查询", notes="存储词源信息-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<WordsEtymology> queryById(@RequestParam(name="id",required=true) String id) {
		WordsEtymology inzWordsEtymology = inzWordsEtymologyService.getById(id);
		if(inzWordsEtymology==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzWordsEtymology);
	}

}
