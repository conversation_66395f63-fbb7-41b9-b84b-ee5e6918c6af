<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.train_plan_info.mapper.InzTrainPlanInfoMapper">

    <resultMap id="BookWordsResultMap" type="org.jeecg.modules.api.book_words.entity.BookWords">
        <!--    <resultMap id="BookWordsResultMap" type="org.jeecg.modules.api.words.entity.Words">-->
        <id property="id" column="word_id"/>
        <result property="bookId" column="book_id"/>
        <result property="wordId" column="word_id"/>
        <result property="status" column="word_status"/>
        <result property="chapterId" column="chapter_id"/>

        <result property="word" column="word_word"/>
        <result property="ukIpa" column="uk_ipa"/>
        <result property="usIpa" column="us_ipa"/>
        <result property="status" column="word_status"/>
        <result property="pronunciationGuide" column="pronunciation_guide"/>
        <result property="rootParticlesMean" column="root_particles_mean"/>
        <result property="type" column="type"/>
        <result property="planInfoId" column="plan_info_id"/>
        <result property="planInfoStatus" column="plan_info_status"/>
        <result property="planInfoLearnType" column="plan_info_learn_type"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="usAudioUrl" column="us_audio_url"/>
        <result property="naturalAudioUrl" column="natural_audio_url"/>
        <result property="breakdownAudioUrl" column="breakdown_audio_url"/>

        <collection property="wordCollocations" ofType="org.jeecg.modules.api.word_collections.entity.WordCollocations">
            <result property="id" column="collocations_id"/>
            <result property="createBy" column="collocations_create_by"/>
            <result property="createTime" column="collocations_create_time"/>
            <result property="updateBy" column="collocations_update_by"/>
            <result property="updateTime" column="collocations_update_time"/>
            <result property="sysOrgCode" column="collocations_sys_org_code"/>
            <result property="wordId" column="collocations_word_id"/>
            <result property="english" column="collocations_english"/>
            <result property="chinese" column="collocations_chinese"/>
            <result property="type" column="collocations_type"/>
            <result property="sort" column="collocations_sort"/>
            <result property="audioUrl" column="collocaions_audio_url"/>
        </collection>
    </resultMap>
    <select id="listWithWords" resultMap="BookWordsResultMap">
        SELECT
        wc.id AS collocations_id,
        wc.create_by AS collocations_create_by,
        wc.create_time AS collocations_create_time,
        wc.update_by AS collocations_update_by,
        wc.update_time AS collocations_update_time,
        wc.sys_org_code AS collocations_sys_org_code,
        wc.word_id AS collocations_word_id,
        wc.english AS collocations_english,
        wc.chinese AS collocations_chinese,
        wc.type AS collocations_type,
        wc.sort AS collocations_sort,
        wc.audio_url AS collocaions_audio_url,
        w.id AS word_id,
        w.word AS word_word,
        w.uk_ipa,
        w.us_ipa,
        w.status AS word_status,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.audio_url,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        w.book_id AS book_id,
        w.chapter_id AS chapter_id,
        itpi.book_id AS book_id,
        itpi.id AS plan_info_id,
        itpi.type as type,
        itpi.status as plan_info_status,
        itpi.learn_type as plan_info_learn_type
        FROM
        (SELECT id, word_id, plan_id, learn_type, type,status,create_time,book_id
        FROM inz_train_plan_info
        WHERE plan_id = #{inzTrainPlanInfo.planId} and day = #{inzTrainPlanInfo.day}
        <if test="inzTrainPlanInfo.learnType != '' and inzTrainPlanInfo.learnType != null">
            AND learn_type = #{inzTrainPlanInfo.learnType}
        </if>
        <if test="inzTrainPlanInfo.type != '' and inzTrainPlanInfo.type != null">
            AND type = #{inzTrainPlanInfo.type}
        </if>
        <if test="inzTrainPlanInfo.finishDay != '' and inzTrainPlanInfo.finishDay != null">
            AND finish_day = #{inzTrainPlanInfo.finishDay}
        </if>
        <if test="inzTrainPlanInfo.type != '' and inzTrainPlanInfo.type != null and inzTrainPlanInfo.type != 2">
            LIMIT #{dailyWordsCount}
        </if>) AS itpi
        LEFT JOIN inz_word_collocations wc ON itpi.word_id = wc.word_id
        LEFT JOIN inz_words w ON itpi.word_id = w.id
        ORDER BY
        <choose>
            <when test="inzTrainPlanInfo.sortBy != null and inzTrainPlanInfo.sortBy == 'rand'">
                RAND()
            </when>
            <when test="inzTrainPlanInfo.sortBy != null and inzTrainPlanInfo.sortBy == 'asc'">
                LEFT(w.word, 1)
            </when>
            <otherwise>
                wc.sort
            </otherwise>
        </choose>;

    </select>
    <select id="getDayLearnCount" resultType="java.util.HashMap">
        SELECT
            COUNT( CASE WHEN learn_type = 1 THEN 1 END ) AS learn_count,
            COUNT( CASE WHEN learn_type = 2 THEN 1 END ) AS review_count
        FROM
            inz_train_plan_info
        WHERE
            plan_id = #{id}
          AND finish_day = #{item};
    </select>
</mapper>

