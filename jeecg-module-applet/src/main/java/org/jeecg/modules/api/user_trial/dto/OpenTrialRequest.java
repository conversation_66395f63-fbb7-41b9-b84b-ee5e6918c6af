package org.jeecg.modules.api.user_trial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Description: 开通试用请求DTO
 * @Author: <PERSON> (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "开通试用请求", description = "开通试用请求参数")
public class OpenTrialRequest {

    /**申请试用天数*/
    @NotNull(message = "试用天数不能为空")
    @Min(value = 1, message = "试用天数最少为1天")
    @Max(value = 8, message = "试用天数最多为8天")
    @ApiModelProperty(value = "申请试用天数", required = true, example = "3")
    private Integer trialDays;

    /**备注*/
    @ApiModelProperty(value = "备注", example = "首次试用申请")
    private String remark;
}
