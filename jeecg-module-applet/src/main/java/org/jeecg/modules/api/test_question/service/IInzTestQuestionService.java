package org.jeecg.modules.api.test_question.service;

import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.api.test_question.entity.InzTestQuestion;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.test_question.entity.InzTestQuestionInfo;

import java.util.List;

/**
 * @Description: 精选试题
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
public interface IInzTestQuestionService extends IService<InzTestQuestion> {

    void genderQuestionOld(InzTestQuestion inzTestQuestion);
    Result<List<InzTestQuestionInfo>> genderQuestion(InzTestQuestion inzTestQuestion);

}
