package org.jeecg.modules.api.user_trial.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 试用状态响应VO
 * @Author: Alex (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "试用状态信息", description = "用户试用状态详细信息")
public class TrialStatusVO {

    /**用户ID*/
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**累计已用试用天数*/
    @ApiModelProperty(value = "累计已用试用天数", example = "3")
    private Integer totalUsedDays;

    /**剩余可用试用天数*/
    @ApiModelProperty(value = "剩余可用试用天数", example = "5")
    private Integer remainingDays;

    /**是否处于试用期*/
    @ApiModelProperty(value = "是否处于试用期", example = "true")
    private Boolean inTrial;

    /**当前试用结束日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "当前试用结束日期", example = "2025-08-02")
    private Date trialEndDate;

    /**试用状态描述*/
    @ApiModelProperty(value = "试用状态描述", example = "试用中")
    private String statusDescription;

    /**最后更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateTime;
}
