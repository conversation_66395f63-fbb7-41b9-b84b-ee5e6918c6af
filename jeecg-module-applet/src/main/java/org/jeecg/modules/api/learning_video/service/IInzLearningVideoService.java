package org.jeecg.modules.api.learning_video.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.learning_video.entity.InzLearningVideo;

import java.util.List;

/**
 * @Description: 视频课程表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IInzLearningVideoService extends IService<InzLearningVideo> {

    /**
     * 根据分类ID获取视频列表
     * @param categoryId 分类ID
     * @return 视频列表
     */
    List<InzLearningVideo> getVideosByCategoryId(String categoryId);

    /**
     * 根据模块ID获取视频列表
     * @param moduleId 模块ID
     * @return 视频列表
     */
    List<InzLearningVideo> getVideosByModuleId(String moduleId);

    /**
     * 获取视频详情（包含关联信息）
     * @param videoId 视频ID
     * @return 视频详情
     */
    InzLearningVideo getVideoDetail(String videoId);

    /**
     * 增加视频观看次数
     * @param videoId 视频ID
     */
    void incrementViewCount(String videoId);

    /**
     * 根据视频编码获取视频
     * @param videoCode 视频编码
     * @return 视频信息
     */
    InzLearningVideo getByVideoCode(String videoCode);

    /**
     * 搜索视频
     * @param keyword 关键词
     * @return 搜索结果
     */
    List<InzLearningVideo> searchVideos(String keyword);

    /**
     * 根据一级分类获取所有视频（包含其下所有二级分类的视频）
     * @param firstLevelCategoryId 一级分类ID
     * @return 视频列表
     */
    List<InzLearningVideo> getVideosByFirstLevelCategory(String firstLevelCategoryId);
}
