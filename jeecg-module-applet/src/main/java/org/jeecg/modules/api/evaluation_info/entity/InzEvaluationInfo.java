package org.jeecg.modules.api.evaluation_info.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 测评详情
 * @Author: jeecg-boot
 * @Date:   2025-01-24
 * @Version: V1.0
 */
@Data
@TableName("inz_evaluation_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_evaluation_info对象", description="测评详情")
public class InzEvaluationInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**测评id*/
	@Excel(name = "测评id", width = 15)
    @ApiModelProperty(value = "测评id")
    private String evaluationId;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sort;
	/**单词id*/
	@Excel(name = "单词id", width = 15)
    @ApiModelProperty(value = "单词id")
    private String wordId;
	/**选择内容*/
	@Excel(name = "选择内容", width = 15)
    @ApiModelProperty(value = "选择内容")
    private String chooseOption;
	/**是否正确 1正确 0错误*/
	@Excel(name = "是否正确 1正确 0错误", width = 15)
    @ApiModelProperty(value = "是否正确 1正确 0错误")
    private Integer isTrue;
}
