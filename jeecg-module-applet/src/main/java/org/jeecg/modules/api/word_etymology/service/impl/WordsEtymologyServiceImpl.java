package org.jeecg.modules.api.word_etymology.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.word_etymology.entity.WordsEtymology;
import org.jeecg.modules.api.word_etymology.mapper.WordsEtymologyMapper;
import org.jeecg.modules.api.word_etymology.service.WordsEtymologyService;
import org.springframework.stereotype.Service;

/**
 * @Description: 存储词源信息
 * @Author: jeecg-boot
 * @Date:   2025-01-16
 * @Version: V1.0
 */
@Service
public class WordsEtymologyServiceImpl extends ServiceImpl<WordsEtymologyMapper, WordsEtymology> implements WordsEtymologyService {

}
