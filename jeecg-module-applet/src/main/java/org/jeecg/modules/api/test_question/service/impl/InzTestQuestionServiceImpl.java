package org.jeecg.modules.api.test_question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.entity.TestQuestionEntity;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.ThirdRequestUtils;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.test_question.entity.InzTestQuestion;
import org.jeecg.modules.api.test_question.entity.InzTestQuestionInfo;
import org.jeecg.modules.api.test_question.entity.InzWordQuestion;
import org.jeecg.modules.api.test_question.mapper.InzTestQuestionMapper;
import org.jeecg.modules.api.test_question.service.IInzTestQuestionInfoService;
import org.jeecg.modules.api.test_question.service.IInzTestQuestionService;
import org.jeecg.modules.api.test_question.service.IInzWordQuestionService;
import org.jeecg.modules.api.words.entity.Words;
import org.jeecg.modules.api.words.service.WordsFrontService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 精选试题
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzTestQuestionServiceImpl extends ServiceImpl<InzTestQuestionMapper, InzTestQuestion> implements IInzTestQuestionService {
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IInzTestQuestionInfoService inzTestQuestionInfoService;

    @Autowired
    private BookWordsService bookWordsService;

    @Autowired
    private WordsFrontService wordsFrontService;

    @Autowired
    private IInzWordQuestionService iInzWordQuestionService;


    @Override
    @Async
    public void genderQuestionOld(InzTestQuestion inzTestQuestion) {
        List<BookWords> list = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzTestQuestion.getBookId()).eq(BookWords::getChapterId, inzTestQuestion.getUnitId()));
        List<String> wordIds = list.stream().map(BookWords::getWordId).collect(Collectors.toList());
        List<Words> wordsList = wordsFrontService.list(new QueryWrapper<Words>().lambda().in(Words::getId, wordIds));
        // 提取词汇名称并拼接
        String wordList = wordsList.stream()
                .map(Words::getWord)
                .collect(Collectors.joining(","));

        // 获取题型
        String questionType = inzTestQuestion.getQuestionType();
        List<String> questionTypes = getQuestionTypes(questionType);

        // 判断是否需要答案
        String isNeedAnswer = inzTestQuestion.getIsNeedAnswer() == 1 ? "需要" : "不需要";

        // 拼接消息
        String otherNeeds = StringUtils.isNotBlank(inzTestQuestion.getOtherNeeds()) ? inzTestQuestion.getOtherNeeds() : "无";
        String sendMessage = String.format("单词：%s。题型：%s。参数答案：%s。题目数量：%s。其他需求：%s",
                wordList,
                String.join(",", questionTypes),
                isNeedAnswer,
                inzTestQuestion.getQuestionCount(),
                otherNeeds);

        String thirdToken = ThirdRequestUtils.getThirdToken("infrabiz", "infrabiz", redisUtil);
        List<TestQuestionEntity> wordEntities = ThirdRequestUtils.analyzeQuestion("",sendMessage,thirdToken,redisUtil);
        ArrayList<InzTestQuestionInfo> inzTestQuestionInfos = new ArrayList<>();
        int sort = 1;
        for (TestQuestionEntity wordEntity : wordEntities) {
            InzTestQuestionInfo inzTestQuestionInfo = new InzTestQuestionInfo();
            BeanUtils.copyProperties(wordEntity,inzTestQuestionInfo);
            inzTestQuestionInfo.setSort(sort);
            inzTestQuestionInfo.setTestQuestionId(inzTestQuestion.getId());
            inzTestQuestionInfo.setOptions(wordEntity.getOptions().toString());
            inzTestQuestionInfo.setOtherContent(wordEntity.getOptions().toString());
            sort++;
            inzTestQuestionInfos.add(inzTestQuestionInfo);
        }
        inzTestQuestionInfoService.saveBatch(inzTestQuestionInfos);
        inzTestQuestion.setFinishTime(new Date());
        update(inzTestQuestion,new QueryWrapper<InzTestQuestion>().lambda().eq(InzTestQuestion::getId, inzTestQuestion.getId()));
    }

    @Override
    public Result<List<InzTestQuestionInfo>> genderQuestion(InzTestQuestion inzTestQuestion) {
        log.info("========== 开始生成精选试题 ==========");
        log.info("请求参数: bookId={}, chapterId={}, questionType={}, questionCount={}",
                 inzTestQuestion.getBookId(), inzTestQuestion.getUnitId(),
                 inzTestQuestion.getQuestionType(), inzTestQuestion.getQuestionCount());

        List<String> questionTypes = getQuestionTypes(inzTestQuestion.getQuestionType());
        log.info("题型编号 '{}' 映射为题型名称: {}", inzTestQuestion.getQuestionType(), questionTypes);

        // 先查询该章节下所有题目的统计信息
        long totalCount = iInzWordQuestionService.count(new QueryWrapper<InzWordQuestion>().lambda()
                .eq(InzWordQuestion::getBookId, inzTestQuestion.getBookId())
                .eq(InzWordQuestion::getChapterId, inzTestQuestion.getUnitId()));
        log.info("该章节总题目数量: {}", totalCount);

        // 查询各题型的数量分布
        for (String questionType : questionTypes) {
            long typeCount = iInzWordQuestionService.count(new QueryWrapper<InzWordQuestion>().lambda()
                    .eq(InzWordQuestion::getBookId, inzTestQuestion.getBookId())
                    .eq(InzWordQuestion::getChapterId, inzTestQuestion.getUnitId())
                    .eq(InzWordQuestion::getQuestionType, questionType));
            log.info("题型 '{}' 的题目数量: {}", questionType, typeCount);
        }

        // 查询题库，随机取题（只查询主题目，parent_id为'0'或null）
        log.info("开始查询主题目，查询条件: bookId={}, chapterId={}, questionTypes={}",
                 inzTestQuestion.getBookId(), inzTestQuestion.getUnitId(), questionTypes);

        List<InzWordQuestion> mainQuestions = iInzWordQuestionService.list(new QueryWrapper<InzWordQuestion>().lambda()
                .eq(InzWordQuestion::getBookId, inzTestQuestion.getBookId())
                .eq(InzWordQuestion::getChapterId, inzTestQuestion.getUnitId())
                .in(InzWordQuestion::getQuestionType, questionTypes)
                .and(wrapper -> wrapper.eq(InzWordQuestion::getParentId, "0").or().isNull(InzWordQuestion::getParentId))
                .last("ORDER BY RAND() LIMIT " + inzTestQuestion.getQuestionCount()));

        log.info("查询到 {} 个主题目", mainQuestions.size());
        for (int i = 0; i < mainQuestions.size(); i++) {
            InzWordQuestion q = mainQuestions.get(i);
            log.info("主题目{}: id={}, type={}, question={}, parentId={}",
                     i+1, q.getId(), q.getQuestionType(),
                     q.getQuestion().length() > 50 ? q.getQuestion().substring(0, 50) + "..." : q.getQuestion(),
                     q.getParentId());
        }

        // 按 questionTypes 顺序排序（题型分组）
        mainQuestions.sort(Comparator.comparingInt(o -> questionTypes.indexOf(o.getQuestionType())));

        // 收集所有题目（包括应用题的子题目）
        List<InzWordQuestion> allQuestions = new ArrayList<>();
        log.info("开始处理主题目，收集子题目...");

        for (InzWordQuestion mainQuestion : mainQuestions) {
            // 添加主题目
            allQuestions.add(mainQuestion);
            log.info("添加主题目: type={}, question={}",
                     mainQuestion.getQuestionType(),
                     mainQuestion.getQuestion().length() > 30 ? mainQuestion.getQuestion().substring(0, 30) + "..." : mainQuestion.getQuestion());

            // 如果是应用题，查询并添加其子题目
            if ("应用题".equals(mainQuestion.getQuestionType())) {
                log.info("发现应用题，开始查询子题目，父题目ID: {}", mainQuestion.getId());

                List<InzWordQuestion> childQuestions = iInzWordQuestionService.list(new QueryWrapper<InzWordQuestion>().lambda()
                        .eq(InzWordQuestion::getParentId, mainQuestion.getId())
                        .orderByAsc(InzWordQuestion::getSort));

                log.info("应用题 {} 包含 {} 个子题目",
                         mainQuestion.getQuestion().length() > 30 ? mainQuestion.getQuestion().substring(0, 30) + "..." : mainQuestion.getQuestion(),
                         childQuestions.size());

                for (int i = 0; i < childQuestions.size(); i++) {
                    InzWordQuestion child = childQuestions.get(i);
                    log.info("  子题目{}: type={}, question={}, parentId={}",
                             i+1, child.getQuestionType(),
                             child.getQuestion().length() > 30 ? child.getQuestion().substring(0, 30) + "..." : child.getQuestion(),
                             child.getParentId());
                }

                allQuestions.addAll(childQuestions);
            }
        }

        log.info("题目收集完成，总共收集到 {} 个题目（包含主题目和子题目）", allQuestions.size());

        ArrayList<InzTestQuestionInfo> inzTestQuestionInfos = new ArrayList<>();
        // 用于映射原始ID到新ID的关系
        Map<String, String> idMapping = new HashMap<>();
        int sort = 1;

        log.info("开始组装题目内容，处理ID映射关系...");

        for (InzWordQuestion wordEntity : allQuestions) {
            InzTestQuestionInfo inzTestQuestionInfo = new InzTestQuestionInfo();

            // 手动设置需要的字段
            inzTestQuestionInfo.setQuestion(wordEntity.getQuestion());
            inzTestQuestionInfo.setQuestionType(wordEntity.getQuestionType());
            inzTestQuestionInfo.setTrueAnswer(wordEntity.getTrueAnswer());
            inzTestQuestionInfo.setParentId(wordEntity.getParentId()); // 先设置原始parent_id

            inzTestQuestionInfo.setSort(sort++);
            inzTestQuestionInfo.setTestQuestionId(inzTestQuestion.getId());
            inzTestQuestionInfo.setOptions(wordEntity.getOptions());
            inzTestQuestionInfo.setOtherContent(wordEntity.getOptions());
            inzTestQuestionInfos.add(inzTestQuestionInfo);

            log.info("准备保存题目: 原始ID={}, 题型={}, parent_id={}",
                     wordEntity.getId(), wordEntity.getQuestionType(), wordEntity.getParentId());
        }

        // 批量保存
        inzTestQuestionInfoService.saveBatch(inzTestQuestionInfos);

        // 保存后建立ID映射关系
        log.info("建立ID映射关系...");
        for (int i = 0; i < allQuestions.size(); i++) {
            String originalId = allQuestions.get(i).getId();
            String newId = inzTestQuestionInfos.get(i).getId();
            idMapping.put(originalId, newId);
            log.info("ID映射: {} -> {}", originalId, newId);
        }

        // 更新parent_id为正确的新ID
        log.info("开始更新parent_id映射关系...");
        for (int i = 0; i < allQuestions.size(); i++) {
            InzWordQuestion originalQuestion = allQuestions.get(i);
            InzTestQuestionInfo newQuestion = inzTestQuestionInfos.get(i);

            String originalParentId = originalQuestion.getParentId();
            if (originalParentId != null && !"0".equals(originalParentId)) {
                String newParentId = idMapping.get(originalParentId);
                if (newParentId != null) {
                    newQuestion.setParentId(newParentId);
                    inzTestQuestionInfoService.updateById(newQuestion);
                    log.info("更新parent_id: 题目ID={}, 原parent_id={}, 新parent_id={}",
                             newQuestion.getId(), originalParentId, newParentId);
                } else {
                    log.warn("未找到父题目ID映射: 原始parent_id={}", originalParentId);
                }
            }
        }
        // 更新完成状态
        inzTestQuestion.setFinishTime(new Date());
        update(inzTestQuestion, new QueryWrapper<InzTestQuestion>().lambda().eq(InzTestQuestion::getId, inzTestQuestion.getId()));

        log.info("精选试题生成完成，共生成 {} 道题目（包含应用题子题目）", inzTestQuestionInfos.size());
        return Result.OK(inzTestQuestionInfos);
    }

    // 提取题型处理逻辑到一个单独的方法
    private List<String> getQuestionTypes(String questionType) {
        log.info("开始解析题型编号: '{}'", questionType);
        List<String> questionTypes = new ArrayList<>();

        // 定义题型映射表 (类型编号 -> 题型名称列表)
        Map<String, List<String>> typeMap = new HashMap<>();
        typeMap.put("1", Collections.singletonList("单选题"));
        typeMap.put("2", Collections.singletonList("多选题"));
        typeMap.put("3", Collections.singletonList("判断题"));
        typeMap.put("4", Collections.singletonList("填空题"));
        typeMap.put("5", Collections.singletonList("应用题"));

        log.info("题型映射表: {}", typeMap);

        if ("0".equals(questionType)) {
            // 如果是0，返回所有题型
            typeMap.values().forEach(questionTypes::addAll);
            log.info("题型编号为0，返回所有题型: {}", questionTypes);
        } else {
            // 分割逗号分隔的题型编号（例如 "1,2,3"）
            String[] typeCodes = questionType.split(",");
            log.info("分割后的题型编号数组: {}", Arrays.toString(typeCodes));

            for (String code : typeCodes) {
                String trimmedCode = code.trim();
                List<String> types = typeMap.get(trimmedCode);
                log.info("题型编号 '{}' 映射为: {}", trimmedCode, types);

                if (types != null) {
                    questionTypes.addAll(types);
                } else {
                    log.warn("未找到题型编号 '{}' 对应的题型名称", trimmedCode);
                }
            }
        }

        log.info("最终解析的题型列表: {}", questionTypes);
        return questionTypes;
    }
}
