package org.jeecg.modules.api.train_plan_info.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 训练计划详情表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
@Data
@TableName("inz_train_plan_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_train_plan_info对象", description="训练计划详情表")
public class InzTrainPlanInfo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**所属计划*/
	@Excel(name = "所属计划", width = 15)
    @ApiModelProperty(value = "所属计划")
    private String planId;
	/**所属单词*/
	@Excel(name = "所属单词", width = 15)
    @ApiModelProperty(value = "所属单词")
    private String wordId;
	/**所属图书*/
	@Excel(name = "所属图书", width = 15)
    @ApiModelProperty(value = "所属图书")
    private String bookId;
	/**类型 0重点词 1需会写 2需会认 3标熟*/
	@Excel(name = "类型 0重点词 1需会写 2需会认 3标熟", width = 15)
    @ApiModelProperty(value = "类型 0重点词 1需会写 2需会认 3标熟")
    private Integer type;
	/**学习时间*/
	@Excel(name = "学习时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "学习时间")
    private Date learnTime;
    /**状态 1未学习 2已学习 已复习*/
    @Excel(name = "状态 1未学习 2已学习", width = 15)
    @ApiModelProperty(value = "状态 1未学习 2已学习")
    private Integer status;

    /**状态 1未学习 2已学习 已复习*/
    @Excel(name = "学习类型 1新学 2复习", width = 15)
    @ApiModelProperty(value = "学习类型 1新学 2复习")
    private Integer learnType;
    @Excel(name = "是否完成 （指当日任务） 1完成 0未完成", width = 15)
    @ApiModelProperty(value = "是否完成 （指当日任务） 1完成 0未完成")
    private Integer isFinish;
    @Excel(name = "完成日期 （指第几天）", width = 15)
    @ApiModelProperty(value = "完成日期 （指第几天）")
    private Integer finishDay;

    @Excel(name = "第几天", width = 15)
    @ApiModelProperty(value = "第几天")
    private Integer day;

    @Excel(name = "0关闭 1开启", width = 15)
    @ApiModelProperty(value = "0关闭 1开启")
    private Integer isLock;

    @TableField(exist = false)
    private String sortBy;
}
