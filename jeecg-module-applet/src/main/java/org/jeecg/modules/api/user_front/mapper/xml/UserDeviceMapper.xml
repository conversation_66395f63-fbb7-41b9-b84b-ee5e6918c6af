<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.user_front.mapper.UserDeviceMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  inz_user_device 
		WHERE
			 user_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="org.jeecg.modules.api.user_front.entity.UserDevice">
		SELECT * 
		FROM  inz_user_device
		WHERE
			 user_id = #{mainId} 	</select>
</mapper>
