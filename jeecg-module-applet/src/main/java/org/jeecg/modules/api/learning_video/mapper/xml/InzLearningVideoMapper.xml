<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.learning_video.mapper.InzLearningVideoMapper">

    <!-- 根据分类ID查询视频列表 -->
    <select id="getVideosByCategoryId" resultType="org.jeecg.modules.api.learning_video.entity.InzLearningVideo">
        SELECT * FROM inz_learning_video 
        WHERE category_id = #{categoryId} 
        AND status = 1 
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据模块ID查询视频列表 -->
    <select id="getVideosByModuleId" resultType="org.jeecg.modules.api.learning_video.entity.InzLearningVideo">
        SELECT * FROM inz_learning_video 
        WHERE module_id = #{moduleId} 
        AND status = 1 
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 查询视频详情（包含模块和分类名称） -->
    <select id="getVideoDetailById" resultType="org.jeecg.modules.api.learning_video.entity.InzLearningVideo">
        SELECT 
            v.*,
            m.module_name as moduleName,
            c.category_name as categoryName
        FROM inz_learning_video v
        LEFT JOIN inz_learning_module m ON v.module_id = m.id
        LEFT JOIN inz_learning_category c ON v.category_id = c.id
        WHERE v.id = #{videoId}
    </select>

    <!-- 增加观看次数 -->
    <update id="incrementViewCount">
        UPDATE inz_learning_video 
        SET view_count = view_count + 1,
            update_time = NOW()
        WHERE id = #{videoId}
    </update>

    <!-- 根据视频编码查询视频 -->
    <select id="getByVideoCode" resultType="org.jeecg.modules.api.learning_video.entity.InzLearningVideo">
        SELECT * FROM inz_learning_video 
        WHERE video_code = #{videoCode}
    </select>

    <!-- 获取热门视频 -->
    <select id="getPopularVideos" resultType="org.jeecg.modules.api.learning_video.entity.InzLearningVideo">
        SELECT * FROM inz_learning_video 
        WHERE status = 1 
        ORDER BY view_count DESC, sort_order ASC 
        LIMIT #{limit}
    </select>

    <!-- 获取免费视频 -->
    <select id="getFreeVideos" resultType="org.jeecg.modules.api.learning_video.entity.InzLearningVideo">
        SELECT * FROM inz_learning_video
        WHERE module_id = #{moduleId}
        AND status = 1
        AND is_free = 1
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据一级分类获取所有视频（包含其下所有二级分类的视频） -->
    <select id="getVideosByFirstLevelCategory" resultType="org.jeecg.modules.api.learning_video.entity.InzLearningVideo">
        SELECT
            v.*,
            c.category_name as categoryName,
            c.parent_id as parentCategoryId
        FROM inz_learning_video v
        INNER JOIN inz_learning_category c ON v.category_id = c.id
        WHERE c.parent_id = #{firstLevelCategoryId}
        AND c.level = 2
        AND c.status = 1
        AND v.status = 1
        ORDER BY c.sort_order ASC, v.sort_order ASC, v.create_time DESC
    </select>

</mapper>
