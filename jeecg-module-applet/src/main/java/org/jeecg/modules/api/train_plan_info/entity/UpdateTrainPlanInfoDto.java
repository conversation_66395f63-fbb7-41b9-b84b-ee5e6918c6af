package org.jeecg.modules.api.train_plan_info.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description: 训练计划详情表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_train_plan_info对象", description="训练计划详情表")
public class UpdateTrainPlanInfoDto implements Serializable {

	/**所属计划*/
    @NotEmpty(message = "所属计划不可为空")
    @ApiModelProperty(value = "所属计划")
    private String planId;
	/**类型 0重点词 1需会写 2需会认 3标熟*/
	@Excel(name = "类型 0重点词 1需会写 2需会认 3标熟", width = 15)
    @ApiModelProperty(value = "类型 0重点词 1需会写 2需会认 3标熟")
    private Integer type;
    /**状态 1未学习 2已学习 已复习*/
    @Excel(name = "状态 1未学习 2已学习", width = 15)
    @ApiModelProperty(value = "状态 1未学习 2已学习")
    private Integer status;
    @ApiModelProperty(value = "状态 1未学习 2已学习")
    private String wordId;
}
