package org.jeecg.modules.api.test_question.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description: 精选试题
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="导出精选试题", description="精选试题")
public class ExportTestQuestionDto implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@NotEmpty(message = "主键id不可为空")
    @ApiModelProperty(value = "主键")
    private String id;
}
