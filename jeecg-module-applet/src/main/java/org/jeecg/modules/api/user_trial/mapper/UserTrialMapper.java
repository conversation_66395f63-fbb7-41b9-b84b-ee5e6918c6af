package org.jeecg.modules.api.user_trial.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.api.user_trial.entity.InzUserTrialLog;
import org.jeecg.modules.api.user_trial.vo.TrialHistoryVO;

import java.util.List;

/**
 * @Description: 用户试用记录Mapper
 * @Author: Alex (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
@Mapper
public interface UserTrialMapper extends BaseMapper<InzUserTrialLog> {

    /**
     * 计算用户累计试用天数
     * @param userId 用户ID
     * @return 累计试用天数
     */
    @Select("SELECT COALESCE(SUM(trial_days), 0) FROM inz_user_trial_log WHERE user_id = #{userId} AND status = 1")
    Integer sumTrialDaysByUserId(@Param("userId") String userId);

    /**
     * 查询用户最新的有效试用记录
     * @param userId 用户ID
     * @return 最新试用记录
     */
    @Select("SELECT * FROM inz_user_trial_log WHERE user_id = #{userId} AND status = 1 ORDER BY create_time DESC LIMIT 1")
    InzUserTrialLog selectLatestValidTrial(@Param("userId") String userId);

    /**
     * 查询用户当前有效的试用记录（未过期）
     * @param userId 用户ID
     * @return 当前有效试用记录
     */
    @Select("SELECT * FROM inz_user_trial_log WHERE user_id = #{userId} AND status = 1 AND end_date >= CURDATE() ORDER BY create_time DESC LIMIT 1")
    InzUserTrialLog selectCurrentValidTrial(@Param("userId") String userId);

    /**
     * 分页查询用户试用历史记录
     * @param page 分页参数
     * @param userId 用户ID
     * @return 试用历史记录
     */
    IPage<TrialHistoryVO> selectTrialHistoryPage(Page<TrialHistoryVO> page, @Param("userId") String userId);

    /**
     * 查询用户所有试用记录
     * @param userId 用户ID
     * @return 试用记录列表
     */
    @Select("SELECT * FROM inz_user_trial_log WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<InzUserTrialLog> selectTrialListByUserId(@Param("userId") String userId);

    /**
     * 统计用户有效试用记录数量
     * @param userId 用户ID
     * @return 有效记录数量
     */
    @Select("SELECT COUNT(*) FROM inz_user_trial_log WHERE user_id = #{userId} AND status = 1")
    Integer countValidTrialsByUserId(@Param("userId") String userId);

    /**
     * 查询指定日期范围内的试用记录
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 试用记录列表
     */
    @Select("SELECT * FROM inz_user_trial_log WHERE user_id = #{userId} AND status = 1 " +
            "AND start_date <= #{endDate} AND end_date >= #{startDate} ORDER BY create_time DESC")
    List<InzUserTrialLog> selectTrialsByDateRange(@Param("userId") String userId, 
                                                  @Param("startDate") String startDate, 
                                                  @Param("endDate") String endDate);
}
