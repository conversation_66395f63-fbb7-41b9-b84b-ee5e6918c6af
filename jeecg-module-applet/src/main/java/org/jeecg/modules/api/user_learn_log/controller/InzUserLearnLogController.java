package org.jeecg.modules.api.user_learn_log.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.user_learn_log.entity.InzUserLearnLog;
import org.jeecg.modules.api.user_learn_log.entity.SaveUserLearnLogDto;
import org.jeecg.modules.api.user_learn_log.service.IInzUserLearnLogService;
import org.jeecg.modules.api.words.entity.Words;
import org.jeecg.modules.api.words.service.WordsFrontService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 用户学习记录
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Api(tags="H5 - 用户学习记录")
@RestController
@RequestMapping("/log")
@Slf4j
public class InzUserLearnLogController extends JeecgController<InzUserLearnLog, IInzUserLearnLogService> {
	@Autowired
	private IInzUserLearnLogService inzUserLearnLogService;

	@Autowired
	private BookWordsService bookWordsService;
    @Autowired
    private WordsFrontService wordsFrontService;

	/**
	 * 分页列表查询
	 *
	 * @param inzUserLearnLog
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "用户学习记录-分页列表查询")
	@ApiOperation(value="用户学习记录-分页列表查询", notes="用户学习记录-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzUserLearnLog>> queryPageList(InzUserLearnLog inzUserLearnLog,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzUserLearnLog> queryWrapper = QueryGenerator.initQueryWrapper(inzUserLearnLog, req.getParameterMap());
		queryWrapper.lambda().eq(StringUtils.isNotBlank(inzUserLearnLog.getBookId()),InzUserLearnLog::getBookId,inzUserLearnLog.getBookId())
				.eq(StringUtils.isNotBlank(inzUserLearnLog.getChapterId()),InzUserLearnLog::getChapterId,inzUserLearnLog.getChapterId())
				.eq(InzUserLearnLog::getCreateBy,CommonUtils.getUserIdByToken())
				.likeRight(InzUserLearnLog::getCreateTime,inzUserLearnLog.getDate());
		Page<InzUserLearnLog> page = new Page<InzUserLearnLog>(pageNo, pageSize);
		IPage<InzUserLearnLog> pageList = inzUserLearnLogService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 用户学习记录s
	 * @param inzUserLearnLog
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "用户学习记录-分页列表查询")
	@ApiOperation(value="用户学习记录-不分页", notes="用户学习记录-不分页")
	@GetMapping(value = "/listAll")
	public Result<List<BookWords>> listAll(InzUserLearnLog inzUserLearnLog, HttpServletRequest req) {
		QueryWrapper<InzUserLearnLog> queryWrapper = QueryGenerator.initQueryWrapper(inzUserLearnLog, req.getParameterMap());
		queryWrapper.lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
				.eq(StringUtils.isNotBlank(inzUserLearnLog.getBookId()),InzUserLearnLog::getBookId,inzUserLearnLog.getBookId())
				.eq(StringUtils.isNotBlank(inzUserLearnLog.getChapterId()),InzUserLearnLog::getChapterId,inzUserLearnLog.getChapterId())
				.likeRight(StringUtils.isNotBlank(inzUserLearnLog.getDate()),InzUserLearnLog::getCreateTime,inzUserLearnLog.getDate());
		List<InzUserLearnLog> pageList = inzUserLearnLogService.list(queryWrapper);
		List<String> wordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
		if(!wordIds.isEmpty()){
			BookWords bookWords = new BookWords();
			bookWords.setWordIds(wordIds);
			bookWords.setCreateBy(CommonUtils.getUserIdByToken());
			List<String> types = new ArrayList<>();
			types.add("part_of_speech");
			types.add("transformation");
			types.add("speak_naturl_phonics");
			types.add("root_particles");
			types.add("natural_phonics");
			types.add("collection");
			types.add("examine");
			types.add("root_breakdown");
			List<BookWords> wordsList = bookWordsService.listWithWords(bookWords, wordIds.size(), types);
			return Result.OK(wordsList);
		} else {
			return Result.OK("当前日期没有学习过噢~~~");
		}

	}

	@Transactional
	@AutoLog(value = "用户学习记录-添加")
	@ApiOperation(value="用户学习记录-添加", notes="用户学习记录-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@Valid @RequestBody SaveUserLearnLogDto saveUserLearnLogDto) {
		String userId = CommonUtils.getUserIdByToken();
		InzUserLearnLog inzUserLearnLog = new InzUserLearnLog();
		BeanUtils.copyProperties(saveUserLearnLogDto, inzUserLearnLog);

		// 动态设置bookId和chapterId（如果通过wordId关联）
		if(StringUtils.isNotBlank(inzUserLearnLog.getWordId())){
			Words words = wordsFrontService.getOne(new QueryWrapper<Words>()
					.lambda()
					.eq(Words::getId, inzUserLearnLog.getWordId()));
			inzUserLearnLog.setChapterId(words.getChapterId());
			inzUserLearnLog.setBookId(words.getBookId());
		}

		// 核心修改：计算当天的开始和结束时间（Date类型适配）
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		Date todayStart = calendar.getTime();

		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		Date todayEnd = calendar.getTime();

		// 构建查询条件
		LambdaQueryWrapper<InzUserLearnLog> queryWrapper = new LambdaQueryWrapper<InzUserLearnLog>()
				.eq(InzUserLearnLog::getCreateBy, userId)
				.eq(StringUtils.isNotBlank(inzUserLearnLog.getBookId()), InzUserLearnLog::getBookId, inzUserLearnLog.getBookId())
				.eq(StringUtils.isNotBlank(inzUserLearnLog.getChapterId()), InzUserLearnLog::getChapterId, inzUserLearnLog.getChapterId())
				.eq(InzUserLearnLog::getWordId, inzUserLearnLog.getWordId())
				.between(InzUserLearnLog::getCreateTime, todayStart, todayEnd); // 使用Date类型范围查询

		// 检查当日记录是否存在
		boolean exists = inzUserLearnLogService.exists(queryWrapper);
		if(exists){
			InzUserLearnLog existingLog = inzUserLearnLogService.getOne(queryWrapper);
			BeanUtils.copyProperties(inzUserLearnLog, existingLog, "id");
			existingLog.setUpdateBy(userId);
			existingLog.setUpdateTime(new Date()); // Date类型直接new
			inzUserLearnLogService.updateById(existingLog);
			return Result.OK("今日记录已更新");
		} else {
			inzUserLearnLog.setCreateBy(userId);
			inzUserLearnLog.setCreateTime(new Date());
			inzUserLearnLogService.save(inzUserLearnLog);
			return Result.OK("添加成功！");
		}
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户学习记录-通过id删除")
	@ApiOperation(value="用户学习记录-通过id删除", notes="用户学习记录-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzUserLearnLogService.remove(new QueryWrapper<InzUserLearnLog>().lambda()
				.eq(InzUserLearnLog::getWordId,id)
				.eq(InzUserLearnLog::getCreateBy,CommonUtils.getUserIdByToken()));
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户学习记录-批量删除")
	@ApiOperation(value="用户学习记录-批量删除", notes="用户学习记录-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzUserLearnLogService.remove(new QueryWrapper<InzUserLearnLog>().lambda()
				.in(InzUserLearnLog::getWordId,Arrays.asList(ids.split(",")))
				.eq(InzUserLearnLog::getCreateBy,CommonUtils.getUserIdByToken()));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "用户学习记录-通过id查询")
	@ApiOperation(value="用户学习记录-通过id查询", notes="用户学习记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzUserLearnLog> queryById(@RequestParam(name="id",required=true) String id) {
		InzUserLearnLog inzUserLearnLog = inzUserLearnLogService.getOne(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getId,id).eq(InzUserLearnLog::getCreateBy,CommonUtils.getUserIdByToken()));
		if(inzUserLearnLog==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzUserLearnLog);
	}

	/**
	 * 查询特定年月内的学习记录日期
	 * @param yearMonth 用户输入的年月，格式为 "yyyy-MM"（例如：2025-01）
	 * @param req 请求对象
	 * @return 返回查询结果，哪些天有学习记录
	 */
	@ApiOperation(value="查询特定年月内学习过的日期", notes="查询特定年月内学习过的日期")
	@GetMapping(value = "/queryLearnedDays")
	public Result<List<String>> queryLearnedDays(
			@RequestParam(name="yearMonth", required=false) String yearMonth,
			HttpServletRequest req) {
		String userId = CommonUtils.getUserIdByToken();
		// 查询条件
		QueryWrapper<InzUserLearnLog> queryWrapper = new QueryWrapper<>();
		if(StringUtils.isNotBlank(yearMonth)){
			// 获取该月的开始日期和结束日期
			Date startDate = getStartDateOfMonth(yearMonth);
			Date endDate = getEndDateOfMonth(yearMonth);
			queryWrapper.ge("create_time", startDate);  // 大于等于该月的开始日期
			queryWrapper.le("create_time", endDate);    // 小于等于该月的结束日期
		}
		queryWrapper.eq("create_by",userId);
		// 查询出符合条件的记录
		List<InzUserLearnLog> learnLogs = inzUserLearnLogService.list(queryWrapper);
//		return Result.OK(learnLogs);
//		// 获取学习过的日期
		Set<String> learnedDays = new HashSet<>();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
				.withZone(ZoneId.of("Asia/Shanghai"));

		for (InzUserLearnLog log : learnLogs) {
			Instant instant = log.getCreateTime().toInstant();
			String day = formatter.format(instant);
			learnedDays.add(day);
		}
		// 将日期按顺序返回
		List<String> result = new ArrayList<>(learnedDays);
		Collections.sort(result);
		return Result.OK(result);
	}

	/**
	 * 获取某年月的开始日期
	 * @param yearMonth 输入的年月（如：2025-01）
	 * @return 返回该月的开始日期
	 */
	private Date getStartDateOfMonth(String yearMonth) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
			Date date = sdf.parse(yearMonth);
			return date;
		} catch (ParseException e) {
			throw new RuntimeException("日期格式解析错误", e);
		}
	}

	/**
	 * 获取某年月的结束日期
	 * @param yearMonth 输入的年月（如：2025-01）
	 * @return 返回该月的结束日期
	 */
	private Date getEndDateOfMonth(String yearMonth) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
			Date date = sdf.parse(yearMonth);
			long time = date.getTime();
			// 将日期调整到下个月的第一天，然后减去一秒就是该月最后一刻的时间
			time += (1000L * 60 * 60 * 24 * 30); // 基本处理
			date.setTime(time);
			return date;
		} catch (ParseException e) {
			throw new RuntimeException("日期格式解析错误", e);
		}
	}

}
