package org.jeecg.modules.api.user_trial.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.user_front.entity.UserFront;
import org.jeecg.modules.api.user_front.service.UserFrontService;
import org.jeecg.modules.api.user_trial.entity.InzUserTrialLog;
import org.jeecg.modules.api.user_trial.mapper.UserTrialMapper;
import org.jeecg.modules.api.user_trial.service.IUserTrialService;
import org.jeecg.modules.api.user_trial.vo.TrialHistoryVO;
import org.jeecg.modules.api.user_trial.vo.TrialOpenResult;
import org.jeecg.modules.api.user_trial.vo.TrialStatusVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Description: 用户试用管理Service实现类
 * @Author: Alex (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
@Service
@Slf4j
public class UserTrialServiceImpl extends ServiceImpl<UserTrialMapper, InzUserTrialLog> implements IUserTrialService {

    @Autowired
    private UserTrialMapper userTrialMapper;

    @Autowired
    private UserFrontService userFrontService;

    /**
     * 试用天数上限常量
     */
    private static final Integer MAX_TRIAL_DAYS = 8;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TrialOpenResult openTrial(String userId, Integer trialDays, String remark) {
        log.info("开通试用 - 用户ID: {}, 申请天数: {}", userId, trialDays);

        // 1. 验证请求参数
        if (StringUtils.isBlank(userId)) {
            throw new JeecgBootException("用户ID不能为空");
        }
        if (trialDays == null || trialDays <= 0) {
            throw new JeecgBootException("试用天数必须大于0");
        }

        // 2. 验证用户状态
        UserFront user = userFrontService.getById(userId);
        if (user == null) {
            throw new JeecgBootException("用户不存在");
        }
        if (user.getStatus() == null || user.getStatus() != 1) {
            throw new JeecgBootException("用户状态异常，无法开通试用");
        }

        // 3. 验证试用天数限制
        if (!validateTrialRequest(userId, trialDays)) {
            Integer remainingDays = calculateRemainingTrialDays(userId);
            throw new JeecgBootException(String.format("试用天数不足，最多还可开通%d天", remainingDays));
        }

        // 4. 创建试用记录
        InzUserTrialLog trialLog = new InzUserTrialLog();
        trialLog.setUserId(userId);
        trialLog.setTrialDays(trialDays);
        trialLog.setStartDate(new Date());
        trialLog.setEndDate(DateUtils.addDays(new Date(), trialDays));
        trialLog.setStatus(1);
        trialLog.setRemark(remark);
        trialLog.setCreateBy(userId);
        trialLog.setCreateTime(new Date());

        boolean saveResult = save(trialLog);
        if (!saveResult) {
            throw new JeecgBootException("试用记录保存失败");
        }

        // 5. 更新用户试用统计信息
        updateUserTrialStatistics(userId);

        // 6. 构建返回结果
        TrialOpenResult result = new TrialOpenResult();
        result.setSuccess(true);
        result.setTrialLogId(trialLog.getId());
        result.setTrialDays(trialDays);
        result.setStartDate(trialLog.getStartDate());
        result.setEndDate(trialLog.getEndDate());
        result.setRemainingDays(calculateRemainingTrialDays(userId));
        result.setMessage("试用开通成功");

        log.info("试用开通成功 - 用户ID: {}, 记录ID: {}", userId, trialLog.getId());
        return result;
    }

    @Override
    public TrialStatusVO getCurrentTrialStatus(String userId) {
        log.debug("查询试用状态 - 用户ID: {}", userId);

        if (StringUtils.isBlank(userId)) {
            throw new JeecgBootException("用户ID不能为空");
        }

        TrialStatusVO status = new TrialStatusVO();
        status.setUserId(userId);

        // 1. 获取用户基本信息
        UserFront user = userFrontService.getById(userId);
        if (user == null) {
            throw new JeecgBootException("用户不存在");
        }

        // 2. 计算试用天数统计
        Integer totalUsedDays = getTotalUsedDays(userId);
        Integer remainingDays = calculateRemainingTrialDays(userId);

        status.setTotalUsedDays(totalUsedDays);
        status.setRemainingDays(remainingDays);

        // 3. 判断当前试用状态
        InzUserTrialLog currentTrial = userTrialMapper.selectCurrentValidTrial(userId);
        if (currentTrial != null) {
            status.setInTrial(true);
            status.setTrialEndDate(currentTrial.getEndDate());
            status.setStatusDescription("试用中");
        } else {
            status.setInTrial(false);
            if (remainingDays > 0) {
                status.setStatusDescription("可开通试用");
            } else {
                status.setStatusDescription("试用天数已用完");
            }
        }

        // 4. 设置最后更新时间
        if (user.getTrialLastUpdate() != null) {
            status.setLastUpdateTime(user.getTrialLastUpdate());
        }

        return status;
    }

    @Override
    public IPage<TrialHistoryVO> getTrialHistory(String userId, Integer pageNo, Integer pageSize) {
        log.debug("查询试用历史 - 用户ID: {}, 页码: {}, 页大小: {}", userId, pageNo, pageSize);

        if (StringUtils.isBlank(userId)) {
            throw new JeecgBootException("用户ID不能为空");
        }

        Page<TrialHistoryVO> page = new Page<>(pageNo, pageSize);
        return userTrialMapper.selectTrialHistoryPage(page, userId);
    }

    @Override
    public Integer calculateRemainingTrialDays(String userId) {
        if (StringUtils.isBlank(userId)) {
            return 0;
        }

        // 1. 查询用户累计试用天数
        Integer totalUsedDays = getTotalUsedDays(userId);

        // 2. 计算剩余天数
        Integer remainingDays = Math.max(0, MAX_TRIAL_DAYS - totalUsedDays);

        // 3. 验证数据一致性（可选，性能考虑）
        UserFront user = userFrontService.getById(userId);
        if (user != null && user.getTrialRemainingDays() != null) {
            if (!user.getTrialRemainingDays().equals(remainingDays)) {
                log.warn("用户试用天数数据不一致 - 用户ID: {}, 计算值: {}, 存储值: {}", 
                        userId, remainingDays, user.getTrialRemainingDays());
                // 可以选择修复数据一致性
                fixTrialDataConsistency(userId);
            }
        }

        return remainingDays;
    }

    @Override
    public boolean validateTrialRequest(String userId, Integer requestDays) {
        if (StringUtils.isBlank(userId) || requestDays == null || requestDays <= 0) {
            return false;
        }

        // 1. 检查申请天数是否超过单次最大限制
        if (requestDays > MAX_TRIAL_DAYS) {
            return false;
        }

        // 2. 检查剩余可用天数
        Integer remainingDays = calculateRemainingTrialDays(userId);
        return requestDays <= remainingDays;
    }

    @Override
    public Integer getTotalUsedDays(String userId) {
        if (StringUtils.isBlank(userId)) {
            return 0;
        }
        Integer totalDays = userTrialMapper.sumTrialDaysByUserId(userId);
        return totalDays != null ? totalDays : 0;
    }

    @Override
    public Boolean isInTrialPeriod(String userId) {
        if (StringUtils.isBlank(userId)) {
            return false;
        }
        InzUserTrialLog currentTrial = userTrialMapper.selectCurrentValidTrial(userId);
        return currentTrial != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean fixTrialDataConsistency(String userId) {
        log.info("修复试用数据一致性 - 用户ID: {}", userId);

        if (StringUtils.isBlank(userId)) {
            return false;
        }

        try {
            // 1. 重新计算累计试用天数
            Integer calculatedTotal = getTotalUsedDays(userId);
            Integer calculatedRemaining = Math.max(0, MAX_TRIAL_DAYS - calculatedTotal);

            // 2. 更新用户表数据
            UserFront user = userFrontService.getById(userId);
            if (user != null) {
                user.setTrialTotalDays(calculatedTotal);
                user.setTrialRemainingDays(calculatedRemaining);
                user.setTrialLastUpdate(new Date());
                
                boolean updateResult = userFrontService.updateById(user);
                if (updateResult) {
                    log.info("试用数据一致性修复成功 - 用户ID: {}, 累计天数: {}, 剩余天数: {}", 
                            userId, calculatedTotal, calculatedRemaining);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("修复试用数据一致性失败 - 用户ID: {}", userId, e);
        }

        return false;
    }

    @Override
    public Object getTrialUsageStatistics() {
        // 这里可以调用Mapper中的统计方法
        // 暂时返回基本统计信息
        return "试用使用统计功能待实现";
    }

    /**
     * 更新用户试用统计信息
     * @param userId 用户ID
     */
    private void updateUserTrialStatistics(String userId) {
        try {
            Integer totalUsedDays = getTotalUsedDays(userId);
            Integer remainingDays = Math.max(0, MAX_TRIAL_DAYS - totalUsedDays);

            UserFront user = userFrontService.getById(userId);
            if (user != null) {
                user.setTrialTotalDays(totalUsedDays);
                user.setTrialRemainingDays(remainingDays);
                user.setTrialLastUpdate(new Date());
                userFrontService.updateById(user);
            }
        } catch (Exception e) {
            log.error("更新用户试用统计信息失败 - 用户ID: {}", userId, e);
        }
    }
}
