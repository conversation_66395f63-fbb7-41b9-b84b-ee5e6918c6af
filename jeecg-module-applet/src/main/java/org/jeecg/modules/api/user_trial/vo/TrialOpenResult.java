package org.jeecg.modules.api.user_trial.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 试用开通结果VO
 * @Author: Alex (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "试用开通结果", description = "试用开通操作结果信息")
public class TrialOpenResult {

    /**是否成功*/
    @ApiModelProperty(value = "是否成功", example = "true")
    private Boolean success;

    /**试用记录ID*/
    @ApiModelProperty(value = "试用记录ID")
    private String trialLogId;

    /**本次试用天数*/
    @ApiModelProperty(value = "本次试用天数", example = "3")
    private Integer trialDays;

    /**试用开始日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "试用开始日期", example = "2025-07-30")
    private Date startDate;

    /**试用结束日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "试用结束日期", example = "2025-08-02")
    private Date endDate;

    /**剩余可用试用天数*/
    @ApiModelProperty(value = "剩余可用试用天数", example = "5")
    private Integer remainingDays;

    /**操作消息*/
    @ApiModelProperty(value = "操作消息", example = "试用开通成功")
    private String message;
}
