import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '单词id',
    align:"center",
    dataIndex: 'wordId'
   },
   {
    title: '词源中文解释',
    align:"center",
    dataIndex: 'originCh'
   },
   {
    title: '意义演变',
    align:"center",
    dataIndex: 'meaningEvolution'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '单词id',
    field: 'wordId',
    component: 'Input',
  },
  {
    label: '词源中文解释',
    field: 'originCh',
    component: 'Input',
  },
  {
    label: '意义演变',
    field: 'meaningEvolution',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  wordId: {title: '单词id',order: 0,view: 'text', type: 'string',},
  originCh: {title: '词源中文解释',order: 1,view: 'text', type: 'string',},
  meaningEvolution: {title: '意义演变',order: 2,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}