package org.jeecg.modules.api.poster_template.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.poster_template.entity.PosterTemplate;
import org.jeecg.modules.api.poster_template.mapper.PosterTemplateMapper;
import org.jeecg.modules.api.poster_template.service.PosterTemplateService;
import org.springframework.stereotype.Service;

/**
 * @Description: 海报表
 * @Author: jeecg-boot
 * @Date:   2025-06-11
 * @Version: V1.0
 */
@Service
public class PosterTemplateServiceImpl extends ServiceImpl<PosterTemplateMapper, PosterTemplate> implements PosterTemplateService {

}
