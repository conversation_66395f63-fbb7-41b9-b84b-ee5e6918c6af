package org.jeecg.modules.api.learning_video.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.learning_video.entity.InzLearningVideo;

import java.util.List;

/**
 * @Description: 视频课程表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface InzLearningVideoMapper extends BaseMapper<InzLearningVideo> {

    /**
     * 根据分类ID查询视频列表
     * @param categoryId 分类ID
     * @return 视频列表
     */
    List<InzLearningVideo> getVideosByCategoryId(@Param("categoryId") String categoryId);

    /**
     * 根据模块ID查询视频列表
     * @param moduleId 模块ID
     * @return 视频列表
     */
    List<InzLearningVideo> getVideosByModuleId(@Param("moduleId") String moduleId);

    /**
     * 查询视频详情（包含模块和分类名称）
     * @param videoId 视频ID
     * @return 视频详情
     */
    InzLearningVideo getVideoDetailById(@Param("videoId") String videoId);

    /**
     * 增加观看次数
     * @param videoId 视频ID
     */
    void incrementViewCount(@Param("videoId") String videoId);

    /**
     * 根据视频编码查询视频
     * @param videoCode 视频编码
     * @return 视频信息
     */
    InzLearningVideo getByVideoCode(@Param("videoCode") String videoCode);

    /**
     * 获取热门视频
     * @param limit 限制数量
     * @return 热门视频列表
     */
    List<InzLearningVideo> getPopularVideos(@Param("limit") Integer limit);

    /**
     * 获取免费视频
     * @param moduleId 模块ID
     * @return 免费视频列表
     */
    List<InzLearningVideo> getFreeVideos(@Param("moduleId") String moduleId);

    /**
     * 根据一级分类获取所有视频（包含其下所有二级分类的视频）
     * @param firstLevelCategoryId 一级分类ID
     * @return 视频列表
     */
    List<InzLearningVideo> getVideosByFirstLevelCategory(@Param("firstLevelCategoryId") String firstLevelCategoryId);
}
