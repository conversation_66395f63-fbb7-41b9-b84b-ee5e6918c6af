package org.jeecg.modules.api.train_plan_info.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.train_plan_info.entity.InzTrainPlanInfo;

import java.util.HashMap;
import java.util.List;

/**
 * @Description: 训练计划详情表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
public interface InzTrainPlanInfoMapper extends BaseMapper<InzTrainPlanInfo> {

    List<BookWords> listWithWords(@Param("inzTrainPlanInfo") InzTrainPlanInfo inzTrainPlanInfo, @Param("dailyWordsCount") Integer dailyWordsCount);

    HashMap<String, Long> getDayLearnCount(@Param("item") Integer item, @Param("id") String id);
}
