package org.jeecg.modules.api.learning_module.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.learning_module.entity.InzLearningModule;
import org.jeecg.modules.api.learning_module.mapper.InzLearningModuleMapper;
import org.jeecg.modules.api.learning_module.service.IInzLearningModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 学习模块表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
public class InzLearningModuleServiceImpl extends ServiceImpl<InzLearningModuleMapper, InzLearningModule> implements IInzLearningModuleService {

    @Autowired
    private InzLearningModuleMapper learningModuleMapper;

    @Override
    public List<InzLearningModule> getEnabledModules() {
        return learningModuleMapper.getModulesByStatus(1);
    }

    @Override
    public void incrementViewCount(String moduleId) {
        learningModuleMapper.incrementViewCount(moduleId);
    }

    @Override
    public InzLearningModule getByModuleCode(String moduleCode) {
        QueryWrapper<InzLearningModule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(InzLearningModule::getModuleCode, moduleCode);
        return getOne(queryWrapper);
    }
}
