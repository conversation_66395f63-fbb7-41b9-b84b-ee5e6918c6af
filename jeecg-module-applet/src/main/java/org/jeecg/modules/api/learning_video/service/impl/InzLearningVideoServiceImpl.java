package org.jeecg.modules.api.learning_video.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.learning_video.entity.InzLearningVideo;
import org.jeecg.modules.api.learning_video.mapper.InzLearningVideoMapper;
import org.jeecg.modules.api.learning_video.service.IInzLearningVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 视频课程表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
public class InzLearningVideoServiceImpl extends ServiceImpl<InzLearningVideoMapper, InzLearningVideo> implements IInzLearningVideoService {

    @Autowired
    private InzLearningVideoMapper learningVideoMapper;

    @Override
    public List<InzLearningVideo> getVideosByCategoryId(String categoryId) {
        return learningVideoMapper.getVideosByCategoryId(categoryId);
    }

    @Override
    public List<InzLearningVideo> getVideosByModuleId(String moduleId) {
        return learningVideoMapper.getVideosByModuleId(moduleId);
    }

    @Override
    public InzLearningVideo getVideoDetail(String videoId) {
        return learningVideoMapper.getVideoDetailById(videoId);
    }

    @Override
    public void incrementViewCount(String videoId) {
        learningVideoMapper.incrementViewCount(videoId);
    }

    @Override
    public InzLearningVideo getByVideoCode(String videoCode) {
        return learningVideoMapper.getByVideoCode(videoCode);
    }

    @Override
    public List<InzLearningVideo> searchVideos(String keyword) {
        QueryWrapper<InzLearningVideo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(InzLearningVideo::getStatus, 1)
                .and(wrapper -> wrapper
                        .like(InzLearningVideo::getVideoTitle, keyword)
                        .or()
                        .like(InzLearningVideo::getDescription, keyword)
                )
                .orderByAsc(InzLearningVideo::getSortOrder);
        return list(queryWrapper);
    }

    @Override
    public List<InzLearningVideo> getVideosByFirstLevelCategory(String firstLevelCategoryId) {
        return learningVideoMapper.getVideosByFirstLevelCategory(firstLevelCategoryId);
    }
}
