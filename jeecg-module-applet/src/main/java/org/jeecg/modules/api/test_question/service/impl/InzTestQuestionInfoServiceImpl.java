package org.jeecg.modules.api.test_question.service.impl;

import org.jeecg.modules.api.test_question.entity.InzTestQuestionInfo;
import org.jeecg.modules.api.test_question.mapper.InzTestQuestionInfoMapper;
import org.jeecg.modules.api.test_question.service.IInzTestQuestionInfoService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 精选试题详情
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Service
public class InzTestQuestionInfoServiceImpl extends ServiceImpl<InzTestQuestionInfoMapper, InzTestQuestionInfo> implements IInzTestQuestionInfoService {

}
