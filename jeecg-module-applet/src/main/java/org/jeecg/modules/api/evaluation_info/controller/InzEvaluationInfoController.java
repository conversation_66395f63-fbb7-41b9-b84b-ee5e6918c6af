package org.jeecg.modules.api.evaluation_info.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.api.evaluation_info.entity.InzEvaluationInfo;
import org.jeecg.modules.api.evaluation_info.service.IInzEvaluationInfoService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 测评详情
 * @Author: jeecg-boot
 * @Date:   2025-01-24
 * @Version: V1.0
 */
@Api(tags="测评详情")
@RestController
@RequestMapping("/evaluation_info/inzEvaluationInfo")
@Slf4j
public class InzEvaluationInfoController extends JeecgController<InzEvaluationInfo, IInzEvaluationInfoService> {
	@Autowired
	private IInzEvaluationInfoService inzEvaluationInfoService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzEvaluationInfo
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "测评详情-分页列表查询")
	@ApiOperation(value="测评详情-分页列表查询", notes="测评详情-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzEvaluationInfo>> queryPageList(InzEvaluationInfo inzEvaluationInfo,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzEvaluationInfo> queryWrapper = QueryGenerator.initQueryWrapper(inzEvaluationInfo, req.getParameterMap());
		Page<InzEvaluationInfo> page = new Page<InzEvaluationInfo>(pageNo, pageSize);
		IPage<InzEvaluationInfo> pageList = inzEvaluationInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param inzEvaluationInfo
	 * @return
	 */
	@AutoLog(value = "测评详情-添加")
	@ApiOperation(value="测评详情-添加", notes="测评详情-添加")
	@RequiresPermissions("evaluation_info:inz_evaluation_info:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzEvaluationInfo inzEvaluationInfo) {
		inzEvaluationInfoService.save(inzEvaluationInfo);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzEvaluationInfo
	 * @return
	 */
	@AutoLog(value = "测评详情-编辑")
	@ApiOperation(value="测评详情-编辑", notes="测评详情-编辑")
	@RequiresPermissions("evaluation_info:inz_evaluation_info:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzEvaluationInfo inzEvaluationInfo) {
		inzEvaluationInfoService.updateById(inzEvaluationInfo);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "测评详情-通过id删除")
	@ApiOperation(value="测评详情-通过id删除", notes="测评详情-通过id删除")
	@RequiresPermissions("evaluation_info:inz_evaluation_info:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzEvaluationInfoService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "测评详情-批量删除")
	@ApiOperation(value="测评详情-批量删除", notes="测评详情-批量删除")
	@RequiresPermissions("evaluation_info:inz_evaluation_info:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzEvaluationInfoService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "测评详情-通过id查询")
	@ApiOperation(value="测评详情-通过id查询", notes="测评详情-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzEvaluationInfo> queryById(@RequestParam(name="id",required=true) String id) {
		InzEvaluationInfo inzEvaluationInfo = inzEvaluationInfoService.getById(id);
		if(inzEvaluationInfo==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzEvaluationInfo);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param inzEvaluationInfo
    */
    @RequiresPermissions("evaluation_info:inz_evaluation_info:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzEvaluationInfo inzEvaluationInfo) {
        return super.exportXls(request, inzEvaluationInfo, InzEvaluationInfo.class, "测评详情");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("evaluation_info:inz_evaluation_info:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, InzEvaluationInfo.class);
    }

}
