package org.jeecg.modules.api.learning_video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 视频课程表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="inz_learning_video对象", description="视频课程表")
@TableName("inz_learning_video")
public class InzLearningVideo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**所属模块ID*/
    @Excel(name = "所属模块ID", width = 15)
    @ApiModelProperty(value = "所属模块ID")
    private String moduleId;

    /**所属分类ID*/
    @Excel(name = "所属分类ID", width = 15)
    @ApiModelProperty(value = "所属分类ID")
    private String categoryId;

    /**视频标题*/
    @Excel(name = "视频标题", width = 30)
    @ApiModelProperty(value = "视频标题")
    private String videoTitle;

    /**视频描述*/
    @Excel(name = "视频描述", width = 50)
    @ApiModelProperty(value = "视频描述")
    private String description;

    /**视频封面URL*/
    @Excel(name = "视频封面URL", width = 30)
    @ApiModelProperty(value = "视频封面URL")
    private String coverImage;

    /**视频文件URL（JSON数组格式存储多个视频）*/
    @Excel(name = "视频文件URL", width = 50)
    @ApiModelProperty(value = "视频文件URL（JSON数组格式）")
    private String videoUrl;

    /**视频URL列表（用于前端展示）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "视频URL列表")
    private List<String> videoUrls;

    /**排序号*/
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**状态 0-禁用 1-启用*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态 0-禁用 1-启用")
    private Integer status;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    /**模块名称（关联查询）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    /**分类名称（关联查询）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "分类名称")
    private String categoryName;
}
