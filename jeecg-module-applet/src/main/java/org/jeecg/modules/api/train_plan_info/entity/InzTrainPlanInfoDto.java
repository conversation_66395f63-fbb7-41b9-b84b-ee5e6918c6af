package org.jeecg.modules.api.train_plan_info.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description: 训练计划详情表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_train_plan_info对象", description="训练计划详情表")
public class InzTrainPlanInfoDto implements Serializable {
	/**所属计划*/
    @NotEmpty(message = "所属计划为必传参数")
    @ApiModelProperty(value = "所属计划",required = true)
    private String planId;

	/**类型 0重点词 1需会写 2需会认 3标熟*/
    @ApiModelProperty(value = "类型 0重点词 1需会写 2需会认 3标熟")
    private Integer type;

    /**状态 1未学习 2已学习 已复习*/
    @ApiModelProperty(value = "学习类型 1新学 2复习")
    private Integer learnType;

    @ApiModelProperty(value = "完成日期 （指第几天）")
    private Integer finishDay;

    @ApiModelProperty(value = "排序方式")
    private String sortBy;
}
