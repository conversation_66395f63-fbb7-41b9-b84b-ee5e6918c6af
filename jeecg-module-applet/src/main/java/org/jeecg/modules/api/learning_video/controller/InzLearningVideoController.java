package org.jeecg.modules.api.learning_video.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.api.learning_video.entity.InzLearningVideo;
import org.jeecg.modules.api.learning_video.service.IInzLearningVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 视频课程表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "H5 - 继续深造 - 视频课程")
@RestController
@RequestMapping("/learning/videos")
@Slf4j
public class InzLearningVideoController extends JeecgController<InzLearningVideo, IInzLearningVideoService> {

    @Autowired
    private IInzLearningVideoService learningVideoService;

    /**
     * 视频课程列表查询（支持分页和不分页）
     */
    @ApiOperation(value = "视频课程-列表查询", notes = "视频课程列表查询，支持分页和不分页模式")
    @GetMapping(value = "/list")
    public Result<?> queryList(
            InzLearningVideo learningVideo,
            @ApiParam(value = "页码，不传则不分页", required = false) @RequestParam(name = "pageNo", required = false) Integer pageNo,
            @ApiParam(value = "页大小", required = false) @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "查询启用状态", required = false) @RequestParam(name = "enabledOnly", defaultValue = "1") Integer enabledOnly,
            HttpServletRequest req) {

        QueryWrapper<InzLearningVideo> queryWrapper = QueryGenerator.initQueryWrapper(learningVideo, req.getParameterMap());

        // 如果指定只查询启用状态
        if (enabledOnly != null) {
            queryWrapper.eq("status", enabledOnly);
        }

        queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");

        // 如果没有传pageNo参数，则不分页返回所有数据
        if (pageNo == null) {
            List<InzLearningVideo> list = learningVideoService.list(queryWrapper);
            return Result.OK(list);
        } else {
            // 分页查询
            Page<InzLearningVideo> page = new Page<>(pageNo, pageSize);
            IPage<InzLearningVideo> pageList = learningVideoService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
    }

    /**
     * 根据分类ID获取视频列表
     */
    @ApiOperation(value = "视频课程-根据分类获取视频", notes = "根据分类ID获取该分类下的所有视频")
    @GetMapping(value = "/category/{categoryId}")
    public Result<List<InzLearningVideo>> getVideosByCategory(
            @ApiParam(value = "分类ID", required = true) @PathVariable("categoryId") String categoryId) {
        List<InzLearningVideo> list = learningVideoService.getVideosByCategoryId(categoryId);
        return Result.OK(list);
    }

    /**
     * 根据模块ID获取视频列表
     */
    @ApiOperation(value = "视频课程-根据模块获取视频", notes = "根据模块ID获取该模块下的所有视频")
    @GetMapping(value = "/module/{moduleId}")
    public Result<List<InzLearningVideo>> getVideosByModule(
            @ApiParam(value = "模块ID", required = true) @PathVariable("moduleId") String moduleId) {
        List<InzLearningVideo> list = learningVideoService.getVideosByModuleId(moduleId);
        return Result.OK(list);
    }

    /**
     * 根据一级分类获取所有视频（包含其下所有二级分类的视频）
     */
    @ApiOperation(value = "视频课程-根据一级分类获取视频", notes = "根据一级分类ID获取该分类下所有二级分类的视频")
    @GetMapping(value = "/first-level-category/{firstLevelCategoryId}")
    public Result<List<InzLearningVideo>> getVideosByFirstLevelCategory(
            @ApiParam(value = "一级分类ID", required = true) @PathVariable("firstLevelCategoryId") String firstLevelCategoryId) {
        List<InzLearningVideo> list = learningVideoService.getVideosByFirstLevelCategory(firstLevelCategoryId);
        return Result.OK(list);
    }

    /**
     * 根据二级分类获取视频列表（推荐使用此接口）
     */
    @ApiOperation(value = "视频课程-根据二级分类获取视频", notes = "根据二级分类ID获取视频列表，这是推荐的获取视频方式")
    @GetMapping(value = "/second-level-category/{secondLevelCategoryId}")
    public Result<List<InzLearningVideo>> getVideosBySecondLevelCategory(
            @ApiParam(value = "二级分类ID", required = true) @PathVariable("secondLevelCategoryId") String secondLevelCategoryId) {
        List<InzLearningVideo> list = learningVideoService.getVideosByCategoryId(secondLevelCategoryId);
        return Result.OK(list);
    }



    /**
     * 搜索视频
     */
    @ApiOperation(value = "视频课程-搜索", notes = "根据关键词搜索视频")
    @GetMapping(value = "/search")
    public Result<List<InzLearningVideo>> searchVideos(
            @ApiParam(value = "搜索关键词", required = true) @RequestParam("keyword") String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return Result.error("搜索关键词不能为空");
        }
        List<InzLearningVideo> list = learningVideoService.searchVideos(keyword.trim());
        return Result.OK(list);
    }

    /**
     * 通过id查询视频详情
     */
    @ApiOperation(value = "视频课程-通过id查询详情", notes = "视频课程-通过id查询详情，包含模块和分类信息")
    @GetMapping(value = "/{id}")
    public Result<InzLearningVideo> queryById(
            @ApiParam(value = "视频ID", required = true) @PathVariable("id") String id) {
        InzLearningVideo learningVideo = learningVideoService.getVideoDetail(id);
        if (learningVideo == null) {
            return Result.error("未找到对应数据");
        }
        
        // 增加观看次数
        learningVideoService.incrementViewCount(id);
        
        return Result.OK(learningVideo);
    }

    /**
     * 根据视频编码查询
     */
    @ApiOperation(value = "视频课程-根据编码查询", notes = "根据视频编码查询视频")
    @GetMapping(value = "/code/{videoCode}")
    public Result<InzLearningVideo> queryByCode(
            @ApiParam(value = "视频编码", required = true) @PathVariable("videoCode") String videoCode) {
        InzLearningVideo learningVideo = learningVideoService.getByVideoCode(videoCode);
        if (learningVideo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(learningVideo);
    }

    /**
     * 添加
     */
    @ApiOperation(value = "视频课程-添加", notes = "视频课程-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzLearningVideo learningVideo) {
        learningVideoService.save(learningVideo);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @ApiOperation(value = "视频课程-编辑", notes = "视频课程-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzLearningVideo learningVideo) {
        learningVideoService.updateById(learningVideo);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value = "视频课程-通过id删除", notes = "视频课程-通过id删除")
    @DeleteMapping(value = "/delete/{id}")
    public Result<String> delete(@PathVariable String id) {
        learningVideoService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @ApiOperation(value = "视频课程-批量删除", notes = "视频课程-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.learningVideoService.removeByIds(java.util.Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }
}
