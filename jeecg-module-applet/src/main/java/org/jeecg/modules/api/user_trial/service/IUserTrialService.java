package org.jeecg.modules.api.user_trial.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.user_trial.entity.InzUserTrialLog;
import org.jeecg.modules.api.user_trial.vo.TrialHistoryVO;
import org.jeecg.modules.api.user_trial.vo.TrialOpenResult;
import org.jeecg.modules.api.user_trial.vo.TrialStatusVO;

/**
 * @Description: 用户试用管理Service接口
 * @Author: Alex (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
public interface IUserTrialService extends IService<InzUserTrialLog> {

    /**
     * 开通试用
     * @param userId 用户ID
     * @param trialDays 试用天数
     * @param remark 备注
     * @return 开通结果
     */
    TrialOpenResult openTrial(String userId, Integer trialDays, String remark);

    /**
     * 获取用户当前试用状态
     * @param userId 用户ID
     * @return 试用状态信息
     */
    TrialStatusVO getCurrentTrialStatus(String userId);

    /**
     * 分页查询用户试用历史记录
     * @param userId 用户ID
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 试用历史记录分页数据
     */
    IPage<TrialHistoryVO> getTrialHistory(String userId, Integer pageNo, Integer pageSize);

    /**
     * 计算用户剩余可用试用天数
     * @param userId 用户ID
     * @return 剩余天数
     */
    Integer calculateRemainingTrialDays(String userId);

    /**
     * 验证试用开通请求
     * @param userId 用户ID
     * @param requestDays 申请天数
     * @return 验证结果
     */
    boolean validateTrialRequest(String userId, Integer requestDays);

    /**
     * 获取用户累计试用天数
     * @param userId 用户ID
     * @return 累计试用天数
     */
    Integer getTotalUsedDays(String userId);

    /**
     * 检查用户是否处于试用期
     * @param userId 用户ID
     * @return 是否在试用期
     */
    Boolean isInTrialPeriod(String userId);

    /**
     * 修复用户试用数据一致性
     * @param userId 用户ID
     * @return 是否修复成功
     */
    Boolean fixTrialDataConsistency(String userId);

    /**
     * 获取试用使用统计信息
     * @return 统计信息
     */
    Object getTrialUsageStatistics();
}
