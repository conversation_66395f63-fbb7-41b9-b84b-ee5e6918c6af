package org.jeecg.modules.api.learning_module.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.learning_module.entity.InzLearningModule;

import java.util.List;

/**
 * @Description: 学习模块表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IInzLearningModuleService extends IService<InzLearningModule> {

    /**
     * 获取所有启用的学习模块
     * @return 模块列表
     */
    List<InzLearningModule> getEnabledModules();

    /**
     * 增加模块观看次数
     * @param moduleId 模块ID
     */
    void incrementViewCount(String moduleId);

    /**
     * 根据模块编码获取模块
     * @param moduleCode 模块编码
     * @return 模块信息
     */
    InzLearningModule getByModuleCode(String moduleCode);
}
