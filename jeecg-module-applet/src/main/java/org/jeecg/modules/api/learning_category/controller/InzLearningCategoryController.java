package org.jeecg.modules.api.learning_category.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.api.learning_category.entity.InzLearningCategory;
import org.jeecg.modules.api.learning_category.service.IInzLearningCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 课程分类表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "H5 - 继续深造 - 课程分类")
@RestController
@RequestMapping("/learning/categories")
@Slf4j
public class InzLearningCategoryController extends JeecgController<InzLearningCategory, IInzLearningCategoryService> {

    @Autowired
    private IInzLearningCategoryService learningCategoryService;

    /**
     * 课程分类列表查询（支持分页和不分页）
     */
    @ApiOperation(value = "课程分类-列表查询", notes = "课程分类列表查询，支持分页和不分页模式")
    @GetMapping(value = "/list")
    public Result<?> queryList(
            InzLearningCategory learningCategory,
            @ApiParam(value = "页码，不传则不分页", required = false) @RequestParam(name = "pageNo", required = false) Integer pageNo,
            @ApiParam(value = "页大小", required = false) @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "查询启用状态", required = false) @RequestParam(name = "enabledOnly", defaultValue = "1") Integer enabledOnly,
            HttpServletRequest req) {

        QueryWrapper<InzLearningCategory> queryWrapper = QueryGenerator.initQueryWrapper(learningCategory, req.getParameterMap());

        // 如果指定只查询启用状态
        if (enabledOnly != null) {
            queryWrapper.eq("status", enabledOnly);
        }

        queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");

        // 如果没有传pageNo参数，则不分页返回所有数据
        if (pageNo == null) {
            List<InzLearningCategory> list = learningCategoryService.list(queryWrapper);
            return Result.OK(list);
        } else {
            // 分页查询
            Page<InzLearningCategory> page = new Page<>(pageNo, pageSize);
            IPage<InzLearningCategory> pageList = learningCategoryService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
    }

    /**
     * 根据模块ID获取分类列表
     */
    @ApiOperation(value = "课程分类-根据模块获取分类", notes = "根据模块ID获取该模块下的所有分类")
    @GetMapping(value = "/module/{moduleId}")
    public Result<List<InzLearningCategory>> getCategoriesByModule(
            @ApiParam(value = "模块ID", required = true) @PathVariable("moduleId") String moduleId) {
        List<InzLearningCategory> list = learningCategoryService.getCategoriesByModuleId(moduleId);
        return Result.OK(list);
    }

    /**
     * 根据模块ID获取分类列表（包含视频信息）
     */
    @ApiOperation(value = "课程分类-获取分类和视频", notes = "根据模块ID获取分类列表，包含每个分类下的视频")
    @GetMapping(value = "/with-videos/{moduleId}")
    public Result<List<InzLearningCategory>> getCategoriesWithVideos(
            @ApiParam(value = "模块ID", required = true) @PathVariable("moduleId") String moduleId) {
        List<InzLearningCategory> list = learningCategoryService.getCategoriesWithVideos(moduleId);
        return Result.OK(list);
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "课程分类-通过id查询", notes = "课程分类-通过id查询")
    @GetMapping(value = "/{id}")
    public Result<InzLearningCategory> queryById(
            @ApiParam(value = "分类ID", required = true) @PathVariable("id") String id) {
        InzLearningCategory learningCategory = learningCategoryService.getById(id);
        if (learningCategory == null) {
            return Result.error("未找到对应数据");
        }

        return Result.OK(learningCategory);
    }

    /**
     * 根据分类编码查询
     */
    @ApiOperation(value = "课程分类-根据编码查询", notes = "根据分类编码查询分类")
    @GetMapping(value = "/code/{categoryCode}")
    public Result<InzLearningCategory> queryByCode(
            @ApiParam(value = "分类编码", required = true) @PathVariable("categoryCode") String categoryCode) {
        InzLearningCategory learningCategory = learningCategoryService.getByCategoryCode(categoryCode);
        if (learningCategory == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(learningCategory);
    }

    /**
     * 获取分类树结构（一级分类及其子分类）
     */
    @ApiOperation(value = "课程分类-获取分类树", notes = "获取模块下的分类树结构，包含一级分类和二级分类")
    @GetMapping(value = "/tree/{moduleId}")
    public Result<List<InzLearningCategory>> getCategoryTree(
            @ApiParam(value = "模块ID", required = true) @PathVariable("moduleId") String moduleId) {
        List<InzLearningCategory> categoryTree = learningCategoryService.getCategoryTree(moduleId);
        return Result.OK(categoryTree);
    }

    /**
     * 获取一级分类列表
     */
    @ApiOperation(value = "课程分类-获取一级分类", notes = "获取模块下的一级分类列表")
    @GetMapping(value = "/first-level/{moduleId}")
    public Result<List<InzLearningCategory>> getFirstLevelCategories(
            @ApiParam(value = "模块ID", required = true) @PathVariable("moduleId") String moduleId) {
        List<InzLearningCategory> categories = learningCategoryService.getFirstLevelCategories(moduleId);
        return Result.OK(categories);
    }

    /**
     * 获取子分类列表
     */
    @ApiOperation(value = "课程分类-获取子分类", notes = "根据父分类ID获取子分类列表")
    @GetMapping(value = "/sub-categories/{parentId}")
    public Result<List<InzLearningCategory>> getSubCategories(
            @ApiParam(value = "父分类ID", required = true) @PathVariable("parentId") String parentId) {
        List<InzLearningCategory> subCategories = learningCategoryService.getSubCategories(parentId);
        return Result.OK(subCategories);
    }

    /**
     * 添加
     */
    @ApiOperation(value = "课程分类-添加", notes = "课程分类-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzLearningCategory learningCategory) {
        learningCategoryService.save(learningCategory);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @ApiOperation(value = "课程分类-编辑", notes = "课程分类-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzLearningCategory learningCategory) {
        learningCategoryService.updateById(learningCategory);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value = "课程分类-通过id删除", notes = "课程分类-通过id删除")
    @DeleteMapping(value = "/delete/{id}")
    public Result<String> delete(@PathVariable String id) {
        learningCategoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @ApiOperation(value = "课程分类-批量删除", notes = "课程分类-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.learningCategoryService.removeByIds(java.util.Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }
}
