package org.jeecg.modules.api.poster_template.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 海报表
 * @Author: jeecg-boot
 * @Date:   2025-06-11
 * @Version: V1.0
 */
@Data
@TableName("inz_poster_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_poster_template对象", description="海报表")
public class PosterTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**海报标题*/
    @Excel(name = "海报标题", width = 15)
    @ApiModelProperty(value = "海报标题")
    private String title;
    /**背景图*/
    @Excel(name = "背景图", width = 15)
    @ApiModelProperty(value = "背景图")
    private String backgroundImage;
    @ApiModelProperty(value = "背景图宽")
    private Integer backgroundImageWidth;
    @ApiModelProperty(value = "背景图高")
    private Integer backgroundImageHeight;
    /**状态 1启用 0禁用*/
    @Excel(name = "状态 1启用 0禁用", width = 15,replace = {"是_Y","否_N"} )
    @ApiModelProperty(value = "状态 1启用 0禁用")
    private Integer status;
    /**二维码开始宽度*/
    @Excel(name = "二维码距左边", width = 15)
    @ApiModelProperty(value = "二维码距左边")
    private Integer qrCodePositionX;
    /**二维码结束高度*/
    @Excel(name = "二维码距顶部", width = 15)
    @ApiModelProperty(value = "二维码距顶部")
    private Integer qrCodePositionY;
    @Excel(name = "宽度占图片宽度的比例", width = 15)
    @ApiModelProperty(value = "宽度占图片宽度的比例")
    private Integer qrCodeSize;
}
