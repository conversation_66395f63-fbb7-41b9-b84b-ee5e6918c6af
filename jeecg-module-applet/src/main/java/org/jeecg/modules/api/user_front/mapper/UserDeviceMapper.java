package org.jeecg.modules.api.user_front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.user_front.entity.UserDevice;

import java.util.List;

/**
 * @Description: 用户设备管理
 * @Author: jeecg-boot
 * @Date:   2025-03-28
 * @Version: V1.0
 */
public interface UserDeviceMapper extends BaseMapper<UserDevice> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<UserDevice>
   */
	public List<UserDevice> selectByMainId(@Param("mainId") String mainId);
}
