import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '测评id',
    align: "center",
    dataIndex: 'evaluationId'
  },
  {
    title: '排序',
    align: "center",
    dataIndex: 'sort'
  },
  {
    title: '单词id',
    align: "center",
    dataIndex: 'wordId'
  },
  {
    title: '选择内容',
    align: "center",
    dataIndex: 'chooseOption'
  },
  {
    title: '是否正确 1正确 0错误',
    align: "center",
    dataIndex: 'isTrue'
  },
];

// 高级查询数据
export const superQuerySchema = {
  evaluationId: {title: '测评id',order: 0,view: 'number', type: 'number',},
  sort: {title: '排序',order: 1,view: 'number', type: 'number',},
  wordId: {title: '单词id',order: 2,view: 'text', type: 'string',},
  chooseOption: {title: '选择内容',order: 3,view: 'text', type: 'string',},
  isTrue: {title: '是否正确 1正确 0错误',order: 4,view: 'number', type: 'number',},
};
