package org.jeecg.modules.api.learning_module.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 学习模块表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="inz_learning_module对象", description="学习模块表")
@TableName("inz_learning_module")
public class InzLearningModule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**模块名称*/
    @Excel(name = "模块名称", width = 15)
    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    /**模块编码*/
    @Excel(name = "模块编码", width = 15)
    @ApiModelProperty(value = "模块编码")
    private String moduleCode;

    /**模块描述*/
    @Excel(name = "模块描述", width = 30)
    @ApiModelProperty(value = "模块描述")
    private String description;

    /**封面图片URL*/
    @Excel(name = "封面图片URL", width = 30)
    @ApiModelProperty(value = "封面图片URL")
    private String coverImage;

    /**排序号*/
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**是否免费 0-收费 1-免费*/
    @Excel(name = "是否免费", width = 15)
    @ApiModelProperty(value = "是否免费 0-收费 1-免费")
    private Integer isFree;

    /**状态 0-禁用 1-启用*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态 0-禁用 1-启用")
    private Integer status;

    /**总视频数量*/
    @Excel(name = "总视频数量", width = 15)
    @ApiModelProperty(value = "总视频数量")
    private Integer totalVideos;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
