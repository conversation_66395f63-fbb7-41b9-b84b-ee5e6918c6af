import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '所属图书',
    align: "center",
    dataIndex: 'bookId'
  },
  {
    title: '所属单元',
    align: "center",
    dataIndex: 'unitId'
  },
  {
    title: '试题类型选择题型 0不限题型  1单选题 2多选题 3判断题 4填空题 5应用题',
    align: "center",
    dataIndex: 'questionType'
  },
  {
    title: '参考答案1需要 0不需要',
    align: "center",
    dataIndex: 'isNeedAnswer'
  },
  {
    title: '其他需求',
    align: "center",
    dataIndex: 'otherNeeds'
  },
  {
    title: '完成时间',
    align: "center",
    dataIndex: 'finishTime'
  },
];

// 高级查询数据
export const superQuerySchema = {
  bookId: {title: '所属图书',order: 0,view: 'text', type: 'string',},
  unitId: {title: '所属单元',order: 1,view: 'text', type: 'string',},
  questionType: {title: '试题类型选择题型 0不限题型  1单选题 2多选题 3判断题 4填空题 5应用题',order: 2,view: 'text', type: 'string',},
  isNeedAnswer: {title: '参考答案1需要 0不需要',order: 3,view: 'number', type: 'number',},
  otherNeeds: {title: '其他需求',order: 4,view: 'text', type: 'string',},
  finishTime: {title: '完成时间',order: 5,view: 'datetime', type: 'string',},
};
