package org.jeecg.modules.api.user_trial.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.api.user_trial.dto.OpenTrialRequest;
import org.jeecg.modules.api.user_trial.entity.InzUserTrialLog;
import org.jeecg.modules.api.user_trial.service.IUserTrialService;
import org.jeecg.modules.api.user_trial.vo.TrialHistoryVO;
import org.jeecg.modules.api.user_trial.vo.TrialOpenResult;
import org.jeecg.modules.api.user_trial.vo.TrialStatusVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @Description: 用户试用管理Controller
 * @Author: Alex (工程师)
 * @Date: 2025-07-30
 * @Version: V1.0
 */
@Api(tags = "H5 - 试用管理")
@RestController
@RequestMapping("/user_trial")
@Slf4j
public class UserTrialController extends JeecgController<InzUserTrialLog, IUserTrialService> {

    @Autowired
    private IUserTrialService userTrialService;

    /**
     * 开通试用
     */
    @AutoLog(value = "开通试用")
    @ApiOperation(value = "开通试用", notes = "用户申请开通试用功能")
    @PostMapping(value = "/openTrial")
    public Result<TrialOpenResult> openTrial(@Valid @RequestBody OpenTrialRequest request, HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            log.info("用户申请开通试用 - 用户ID: {}, 申请天数: {}", userId, request.getTrialDays());
            
            TrialOpenResult result = userTrialService.openTrial(userId, request.getTrialDays(), request.getRemark());
            return Result.OK(result);
        } catch (Exception e) {
            log.error("开通试用失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 查询试用状态
     */
    @AutoLog(value = "查询试用状态")
    @ApiOperation(value = "查询试用状态", notes = "查询用户当前试用状态信息")
    @GetMapping(value = "/getTrialStatus")
    public Result<TrialStatusVO> getTrialStatus(HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            log.debug("查询用户试用状态 - 用户ID: {}", userId);
            
            TrialStatusVO status = userTrialService.getCurrentTrialStatus(userId);
            return Result.OK(status);
        } catch (Exception e) {
            log.error("查询试用状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 查询试用历史记录
     */
    @AutoLog(value = "查询试用历史记录")
    @ApiOperation(value = "查询试用历史记录", notes = "分页查询用户试用历史记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "页码", defaultValue = "1", dataType = "Integer"),
            @ApiImplicitParam(name = "pageSize", value = "页大小", defaultValue = "10", dataType = "Integer")
    })
    @GetMapping(value = "/getTrialHistory")
    public Result<IPage<TrialHistoryVO>> getTrialHistory(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            log.debug("查询用户试用历史 - 用户ID: {}, 页码: {}, 页大小: {}", userId, pageNo, pageSize);
            
            IPage<TrialHistoryVO> page = userTrialService.getTrialHistory(userId, pageNo, pageSize);
            return Result.OK(page);
        } catch (Exception e) {
            log.error("查询试用历史记录失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 查询剩余试用天数
     */
    @AutoLog(value = "查询剩余试用天数")
    @ApiOperation(value = "查询剩余试用天数", notes = "查询用户剩余可用试用天数")
    @GetMapping(value = "/getRemainingDays")
    public Result<Integer> getRemainingDays(HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            log.debug("查询用户剩余试用天数 - 用户ID: {}", userId);
            
            Integer remainingDays = userTrialService.calculateRemainingTrialDays(userId);
            return Result.OK(remainingDays);
        } catch (Exception e) {
            log.error("查询剩余试用天数失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 验证试用申请
     */
    @AutoLog(value = "验证试用申请")
    @ApiOperation(value = "验证试用申请", notes = "验证用户试用申请是否有效")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "trialDays", value = "申请试用天数", required = true, dataType = "Integer")
    })
    @GetMapping(value = "/validateTrialRequest")
    public Result<Boolean> validateTrialRequest(
            @RequestParam(name = "trialDays") Integer trialDays,
            HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            log.debug("验证用户试用申请 - 用户ID: {}, 申请天数: {}", userId, trialDays);
            
            boolean isValid = userTrialService.validateTrialRequest(userId, trialDays);
            return Result.OK(isValid);
        } catch (Exception e) {
            log.error("验证试用申请失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查是否处于试用期
     */
    @AutoLog(value = "检查试用期状态")
    @ApiOperation(value = "检查试用期状态", notes = "检查用户是否处于试用期")
    @GetMapping(value = "/isInTrialPeriod")
    public Result<Boolean> isInTrialPeriod(HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            log.debug("检查用户试用期状态 - 用户ID: {}", userId);
            
            Boolean inTrial = userTrialService.isInTrialPeriod(userId);
            return Result.OK(inTrial);
        } catch (Exception e) {
            log.error("检查试用期状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修复数据一致性（管理员功能）
     */
    @AutoLog(value = "修复试用数据一致性")
    @ApiOperation(value = "修复试用数据一致性", notes = "修复用户试用数据一致性（管理员功能）")
    @PostMapping(value = "/fixDataConsistency")
    public Result<Boolean> fixDataConsistency(HttpServletRequest req) {
        try {
            String userId = CommonUtils.getUserIdByToken();
            log.info("修复用户试用数据一致性 - 用户ID: {}", userId);
            
            Boolean result = userTrialService.fixTrialDataConsistency(userId);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("修复试用数据一致性失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取试用使用统计（管理员功能）
     */
    @AutoLog(value = "获取试用使用统计")
    @ApiOperation(value = "获取试用使用统计", notes = "获取试用使用统计信息（管理员功能）")
    @GetMapping(value = "/getUsageStatistics")
    public Result<Object> getUsageStatistics(HttpServletRequest req) {
        try {
            log.info("获取试用使用统计信息");
            
            Object statistics = userTrialService.getTrialUsageStatistics();
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取试用使用统计失败", e);
            return Result.error(e.getMessage());
        }
    }
}
