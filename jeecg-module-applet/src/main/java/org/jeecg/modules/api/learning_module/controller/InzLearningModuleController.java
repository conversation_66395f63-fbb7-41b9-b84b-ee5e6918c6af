package org.jeecg.modules.api.learning_module.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.api.learning_module.entity.InzLearningModule;
import org.jeecg.modules.api.learning_module.service.IInzLearningModuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 学习模块表
 * @Author: jeecg-boot
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "H5 - 继续深造 - 学习模块")
@RestController
@RequestMapping("/learning/modules")
@Slf4j
public class InzLearningModuleController extends JeecgController<InzLearningModule, IInzLearningModuleService> {

    @Autowired
    private IInzLearningModuleService learningModuleService;

    /**
     * 学习模块列表查询（支持分页和不分页）
     */
    @ApiOperation(value = "学习模块-列表查询", notes = "学习模块列表查询，支持分页和不分页模式")
    @GetMapping(value = "/list")
    public Result<?> queryList(
            InzLearningModule learningModule,
            @ApiParam(value = "页码，不传则不分页", required = false) @RequestParam(name = "pageNo", required = false) Integer pageNo,
            @ApiParam(value = "页大小", required = false) @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "只查询启用状态", required = false) @RequestParam(name = "enabledOnly", defaultValue = "1") Integer enabledOnly,
            HttpServletRequest req) {

        QueryWrapper<InzLearningModule> queryWrapper = QueryGenerator.initQueryWrapper(learningModule, req.getParameterMap());
        if (enabledOnly != null ) {
            queryWrapper.eq("status", enabledOnly);
        }

        queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");

        // 如果没有传pageNo参数，则不分页返回所有数据
        if (pageNo == null) {
            List<InzLearningModule> list = learningModuleService.list(queryWrapper);
            return Result.OK(list);
        } else {
            // 分页查询
            Page<InzLearningModule> page = new Page<>(pageNo, pageSize);
            IPage<InzLearningModule> pageList = learningModuleService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "学习模块-通过id查询", notes = "学习模块-通过id查询")
    @GetMapping(value = "/{id}")
    public Result<InzLearningModule> queryById(
            @ApiParam(value = "模块ID", required = true) @PathVariable("id") String id) {
        InzLearningModule learningModule = learningModuleService.getById(id);
        if (learningModule == null) {
            return Result.error("未找到对应数据");
        }
        
        // 增加观看次数
        learningModuleService.incrementViewCount(id);
        
        return Result.OK(learningModule);
    }

    /**
     * 根据模块编码查询
     */
    @ApiOperation(value = "学习模块-根据编码查询", notes = "根据模块编码查询学习模块")
    @GetMapping(value = "/code/{moduleCode}")
    public Result<InzLearningModule> queryByCode(
            @ApiParam(value = "模块编码", required = true) @PathVariable("moduleCode") String moduleCode) {
        InzLearningModule learningModule = learningModuleService.getByModuleCode(moduleCode);
        if (learningModule == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(learningModule);
    }

    /**
     * 添加
     */
    @ApiOperation(value = "学习模块-添加", notes = "学习模块-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzLearningModule learningModule) {
        learningModuleService.save(learningModule);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @ApiOperation(value = "学习模块-编辑", notes = "学习模块-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzLearningModule learningModule) {
        learningModuleService.updateById(learningModule);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value = "学习模块-通过id删除", notes = "学习模块-通过id删除")
    @DeleteMapping(value = "/delete/{id}")
    public Result<String> delete(@PathVariable String id) {
        learningModuleService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @ApiOperation(value = "学习模块-批量删除", notes = "学习模块-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.learningModuleService.removeByIds(java.util.Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }
}
