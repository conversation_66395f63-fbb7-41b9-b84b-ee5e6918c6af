package org.jeecg.modules.user_front.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.config.entity.InzConfig;
import org.jeecg.modules.config.service.IInzConfigService;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.jeecg.modules.user_front.service.IInzUserTrialLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
// 在文件顶部添加验证注解的import
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * @Description: 后台权限开通管理Controller
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "后台管理 - 权限开通管理")
@RestController
@RequestMapping("/user_front/permission")
@Slf4j
public class PermissionGrantController extends JeecgController<InzUserFront, IInzUserFrontService> {

    @Autowired
    private IInzUserFrontService inzUserFrontService;

    @Autowired
    private IInzConfigService inzConfigService;

    /**
     * 获取指定用户的剩余试用天数
     */
    @AutoLog(value = "获取用户剩余试用天数")
    @ApiOperation(value = "获取用户剩余试用天数", notes = "获取指定前台用户的剩余可配置试用天数")
    @GetMapping(value = "/getUserRemainingDays")
    public Result<Integer> getUserRemainingDays(
            @ApiParam(value = "前台用户ID", required = true) @RequestParam("userId") String userId,
            HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.debug("获取用户剩余试用天数 - 操作者: {}, 目标用户: {}", operatorId, userId);

            // 验证权限：检查目标用户是否在操作者的管辖范围内
            if (!validateUserManagementPermission(operatorId, userId)) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // 获取用户剩余试用天数
            Integer remainingDays = calculateRemainingTrialDays(userId);
            log.info("用户剩余试用天数查询成功 - 用户: {}, 剩余天数: {}", userId, remainingDays);
            return Result.OK(remainingDays);
        } catch (Exception e) {
            log.error("获取用户剩余试用天数失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 开通试用权限
     */
    @AutoLog(value = "开通试用权限")
    @ApiOperation(value = "开通试用权限", notes = "代理商为用户开通试用权限")
    @PostMapping(value = "/grantTrial")
    public Result<String> grantTrial(@Valid @RequestBody PermissionGrantRequest request, HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.info("开通试用权限 - 操作者: {}, 目标用户: {}, 天数: {}",
                    operatorId, request.getUserId(), request.getDuration());

            // 验证权限
            if (!validateUserManagementPermission(operatorId, request.getUserId())) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // 验证试用天数
            Integer remainingDays = calculateRemainingTrialDays(request.getUserId());
            if (request.getDuration() > remainingDays) {
                return Result.error("配置天数不能超过剩余可用天数(" + remainingDays + "天)");
            }

            // 执行开通逻辑
            boolean success = grantTrialPermission(request, operatorId);

            if (success) {
                log.info("试用权限开通成功 - 操作者: {}, 目标用户: {}, 天数: {}",
                        operatorId, request.getUserId(), request.getDuration());
                return Result.OK("试用权限开通成功");
            } else {
                return Result.error("试用权限开通失败");
            }
        } catch (Exception e) {
            log.error("开通试用权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 开通全年权限
     */
    @AutoLog(value = "开通全年权限")
    @ApiOperation(value = "开通全年权限", notes = "代理商为用户开通全年权限")
    @PostMapping(value = "/grantAnnual")
    public Result<String> grantAnnual(@Valid @RequestBody PermissionGrantRequest request, HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.info("开通全年权限 - 操作者: {}, 目标用户: {}", operatorId, request.getUserId());

            // 验证权限
            if (!validateUserManagementPermission(operatorId, request.getUserId())) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // 执行开通逻辑
            boolean success = grantAnnualPermission(request, operatorId);

            if (success) {
                log.info("全年权限开通成功 - 操作者: {}, 目标用户: {}", operatorId, request.getUserId());
                LambdaQueryWrapper<InzUserFront> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(InzUserFront::getId, request.getUserId());
                InzUserFront userFront = inzUserFrontService.getOne(queryWrapper);
                userFront.setFormalUser("1");
                inzUserFrontService.updateById(userFront);
                return Result.OK("全年权限开通成功");
            } else {
                return Result.error("全年权限开通失败");
            }
        } catch (Exception e) {
            log.error("开通全年权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取权限开通历史
     */
    @AutoLog(value = "获取权限开通历史")
    @ApiOperation(value = "获取权限开通历史", notes = "获取指定用户的权限开通历史记录")
    @GetMapping(value = "/getGrantHistory")
    public Result<String> getGrantHistory(
            @ApiParam(value = "前台用户ID", required = true) @RequestParam("userId") String userId,
            @ApiParam(value = "页码", required = false) @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "页大小", required = false) @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.debug("获取权限开通历史 - 操作者: {}, 目标用户: {}", operatorId, userId);

            // 验证权限
            if (!validateUserManagementPermission(operatorId, userId)) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // TODO: 实现权限开通历史查询逻辑
            log.info("查询权限开通历史 - 用户: {}, 页码: {}, 页大小: {}", userId, pageNo, pageSize);

            return Result.OK("权限开通历史查询功能待实现");
        } catch (Exception e) {
            log.error("获取权限开通历史失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 验证用户管理权限
     * 将后台用户ID转换为前台用户ID，然后验证管辖范围
     */
    private boolean validateUserManagementPermission(String backendUserId, String frontendUserId) {
        try {
            // 1. 根据后台用户ID查找对应的前台用户
            InzUserFront frontendAgent = inzUserFrontService.getByBackendUserId(backendUserId);

            if (frontendAgent == null) {
                log.warn("后台用户没有对应的前台用户 - 后台用户ID: {}", backendUserId);
                return false;
            }

            // 2. 使用前台用户ID验证管辖范围
            String frontendAgentId = frontendAgent.getId();
            boolean hasPermission = isUserInManagementScope(frontendAgentId, frontendUserId);

            log.debug("权限验证结果 - 后台用户: {}, 前台代理商: {}, 目标用户: {}, 有权限: {}",
                     backendUserId, frontendAgentId, frontendUserId, hasPermission);

            return hasPermission;
        } catch (Exception e) {
            log.error("验证用户管理权限失败 - 后台用户: {}, 目标用户: {}", backendUserId, frontendUserId, e);
            return false;
        }
    }

    /**
     * 检查用户是否在管辖范围内
     */
    private boolean isUserInManagementScope(String agentId, String userId) {
        return checkUserHierarchy(agentId, userId);
    }

    /**
     * 检查用户层级关系
     */
    private boolean checkUserHierarchy(String agentId, String userId) {
        try {
            InzUserFront user = inzUserFrontService.getById(userId);
            if (user == null) {
                return false;
            }

            // 检查直接上级关系
            if (agentId.equals(user.getParentId())) {
                return true;
            }

            // 检查间接上级关系（递归查找）
            String currentParentId = user.getParentId();
            int maxDepth = 10; // 防止无限递归
            int depth = 0;

            while (currentParentId != null && depth < maxDepth) {
                if (agentId.equals(currentParentId)) {
                    return true;
                }

                InzUserFront parent = inzUserFrontService.getById(currentParentId);
                if (parent == null) {
                    break;
                }

                currentParentId = parent.getParentId();
                depth++;
            }

            return false;
        } catch (Exception e) {
            log.error("检查用户层级关系失败 - 代理商: {}, 用户: {}", agentId, userId, e);
            return false;
        }
    }

    /**
     * 计算剩余试用天数
     */
    private Integer calculateRemainingTrialDays(String userId) {
        // 这里需要实现具体的试用天数计算逻辑
        // 最大试用天数 - 已使用天数 = 剩余天数
        LambdaQueryWrapper<InzConfig> wrapper = new LambdaQueryWrapper<InzConfig>();
        wrapper.eq(InzConfig::getCode, "FREE_MEMBERSHIP_DAYS");
        InzConfig config = inzConfigService.getOne(wrapper);
        int maxTrialDays = Integer.parseInt(config.getValue());
        LambdaQueryWrapper<InzUserFront> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InzUserFront::getId, userId);
        InzUserFront userFront = inzUserFrontService.getOne(queryWrapper);
        int usedDays = Integer.parseInt(userFront.getFormalUser());
        return Math.max(0, maxTrialDays - usedDays);
    }

    /**
     * 开通试用权限的具体实现
     */
    @Autowired
    private IInzUserTrialLogService inzUserTrialLogService;


    private boolean grantTrialPermission(PermissionGrantRequest request) {
        try {
            // 1. 获取当前登录用户作为操作者
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String operatorId = loginUser.getId();

            // 2. 验证用户是否存在
            InzUserFront user = inzUserFrontService.getById(request.getUserId());
            if (user == null) {
                log.error("用户不存在 - 用户ID: {}", request.getUserId());
                return false;
            }

            // 3. 检查用户剩余试用天数
            Integer remainingDays = inzUserTrialLogService.calculateRemainingDays(request.getUserId());
            if (remainingDays <= 0) {
                log.warn("用户试用天数已用完 - 用户ID: {}, 剩余天数: {}", request.getUserId(), remainingDays);
                return false;
            }

            // 4. 检查请求的天数是否超过剩余天数
            if (request.getDuration() > remainingDays) {
                log.warn("请求天数超过剩余试用天数 - 用户ID: {}, 请求: {}天, 剩余: {}天",
                        request.getUserId(), request.getDuration(), remainingDays);
                return false;
            }

            // 5. 添加试用记录（记录试用天数的使用）
            String content = String.format("开通教育系列[%s]试用权限，使用%d天试用时间",
                                         request.getEducationId(), request.getDuration());
            boolean logSuccess = inzUserTrialLogService.addTrialDays(
                request.getUserId(),
                operatorId,
                request.getDuration(),
                content,
                request.getEducationId()
            );

            if (!logSuccess) {
                log.error("添加试用记录失败 - 用户ID: {}", request.getUserId());
                return false;
            }

            // 6. 更新用户试用信息（可选，如果需要在InzUserFront表中同步）
            updateUserTrialInfo(request.getUserId());

            // 7. 这里可以添加具体的权限开通逻辑，比如：
            // - 开通对应教育系列的访问权限
            // - 设置权限过期时间
            // - 发送通知等

            log.info("试用权限开通成功 - 用户ID: {}, 教育系列: {}, 天数: {}",
                    request.getUserId(), request.getEducationId(), request.getDuration());

            return true;

        } catch (Exception e) {
            log.error("开通试用权限失败 - 用户ID: {}", request.getUserId(), e);
            return false;
        }
    }

    /**
     * 更新用户试用信息
     */
    private void updateUserTrialInfo(String userId) {
        try {
            InzUserFront user = inzUserFrontService.getById(userId);
            if (user != null) {
                // 计算最新的剩余天数
                Integer remainingDays = inzUserTrialLogService.calculateRemainingDays(userId);

                // 更新用户表中的试用信息
                user.setTrialRemainingDays(remainingDays);
                user.setTrialLastUpdate(new Date());

                inzUserFrontService.updateById(user);

                log.debug("更新用户试用信息成功 - 用户ID: {}, 剩余天数: {}", userId, remainingDays);
            }
        } catch (Exception e) {
            log.error("更新用户试用信息失败 - 用户ID: {}", userId, e);
        }
    }

    /**
     * 开通全年权限的具体实现
     */
    private boolean grantAnnualPermission(PermissionGrantRequest request) {
        try {
            // 1. 获取当前登录用户作为操作者
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String operatorId = loginUser.getId();

            // 2. 验证用户是否存在
            InzUserFront user = inzUserFrontService.getById(request.getUserId());
            if (user == null) {
                log.error("用户不存在 - 用户ID: {}", request.getUserId());
                return false;
            }

            // 3. 检查操作者的金豆是否足够（假设年度权限需要消耗金豆）
            InzUserFront operator = inzUserFrontService.getById(operatorId);
            if (operator == null || operator.getGoldenBean() < getAnnualPermissionCost()) {
                log.warn("操作者金豆不足 - 操作者ID: {}, 当前金豆: {}, 需要: {}",
                        operatorId, operator != null ? operator.getGoldenBean() : 0, getAnnualPermissionCost());
                return false;
            }

            // 4. 扣除金豆
            operator.setGoldenBean(operator.getGoldenBean() - getAnnualPermissionCost());
            inzUserFrontService.updateById(operator);

            // 5. 记录金豆消费日志
            recordGoldenBeanConsumption(operatorId, request.getUserId(), request.getEducationId());

            // 6. 设置用户年度权限
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, 1);
            user.setVipTime(calendar.getTime());
            inzUserFrontService.updateById(user);

            // 7. 记录操作日志
            String content = String.format("开通教育系列[%s]年度权限，有效期至%s",
                                         request.getEducationId(),
                                         new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime()));

            log.info("年度权限开通成功 - 用户ID: {}, 教育系列: {}, 有效期至: {}",
                    request.getUserId(), request.getEducationId(), calendar.getTime());

            return true;

        } catch (Exception e) {
            log.error("开通年度权限失败 - 用户ID: {}", request.getUserId(), e);
            return false;
        }
    }

    /**
     * 权限开通请求DTO
     */
    @Data
    public static class PermissionGrantRequest {
        @NotNull(message = "用户ID不能为空")
        private String userId;

        @NotNull(message = "教育系列ID不能为空")
        private String educationId;  // 原educationSeriesId改为educationId

        @NotNull(message = "试用天数不能为空")
        @Min(value = 1, message = "试用天数不能少于1天")
        @Max(value = 365, message = "试用天数不能超过365天")
        private Integer duration;    // 原daysCount改为duration
    }
}

