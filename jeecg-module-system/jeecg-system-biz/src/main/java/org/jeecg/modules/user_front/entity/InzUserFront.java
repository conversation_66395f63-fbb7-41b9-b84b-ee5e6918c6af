package org.jeecg.modules.user_front.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@ApiModel(value="inz_user_front对象", description="用户表")
@Data
@TableName("inz_user_front")
public class InzUserFront implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**真实姓名*/
	@Excel(name = "真实姓名", width = 15)
    @ApiModelProperty(value = "真实姓名")
    private String realName;
	/**年级*/
	@Excel(name = "年级", width = 15)
    @ApiModelProperty(value = "年级")
    private String grade;
	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String phone;
	/**密码*/
	@Excel(name = "密码", width = 15)
    @ApiModelProperty(value = "密码")
    private String password;
	/**地址*/
	@Excel(name = "地址", width = 15)
    @ApiModelProperty(value = "地址")
    private String address;
	/**名字*/
	@Excel(name = "名字", width = 15)
    @ApiModelProperty(value = "名字")
    private String name;
	/**公众号昵称*/
	@Excel(name = "公众号昵称", width = 15)
    @ApiModelProperty(value = "公众号昵称")
    private String oaNickname;
	/**公众号 OpenId*/
	@Excel(name = "公众号 OpenId", width = 15)
    @ApiModelProperty(value = "公众号 OpenId")
    private String oaOpenid;
	/**小程序OpenId*/
	@Excel(name = "小程序OpenId", width = 15)
    @ApiModelProperty(value = "小程序OpenId")
    private String mpOpenid;
	/**企业微信 OpenId*/
	@Excel(name = "企业微信 OpenId", width = 15)
    @ApiModelProperty(value = "企业微信 OpenId")
    private String ewOpenid;
	/**联合 Id*/
	@Excel(name = "联合 Id", width = 15)
    @ApiModelProperty(value = "联合 Id")
    private String unionId;
	/**角色*/
	@Excel(name = "角色", width = 15)
    @ApiModelProperty(value = "角色")
    private String role;
	/**状态 (1正常 0停用)*/
	@Excel(name = "状态 (1正常 0停用)", width = 15)
    @ApiModelProperty(value = "状态 (1正常 0停用)")
    private Integer status;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**头像*/
	@Excel(name = "头像", width = 15)
    @ApiModelProperty(value = "头像")
    private String avatar;
	/**最后一次进入系统的时间*/
	@Excel(name = "最后一次进入系统的时间", width = 15)
    @ApiModelProperty(value = "最后一次进入系统的时间")
    private Date lastUseAt;
	/**会员时间*/
	@Excel(name = "会员时间", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "会员时间")
    private Date vipTime;

    /**累计试用天数*/
    @ApiModelProperty(value = "累计试用天数")
    private Integer trialTotalDays;

    /**剩余可用试用天数*/
    @ApiModelProperty(value = "剩余可用试用天数")
    private Integer trialRemainingDays;

    /**试用信息最后更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "试用信息最后更新时间")
    private Date trialLastUpdate;

    @ApiModelProperty(value = "邀请人Id")
    private String parentId;

    @ApiModelProperty(value = "金豆数量")
    private Integer goldenBean;

    @ApiModelProperty(value = "密码盐")
    private String salt;

    @TableField(exist = false)
    private String tryStudy;
    /**是否为正式用户（1是，0否）*/
    @ApiModelProperty(value = "是否为正式用户（1是，0否）")
    @TableField(exist = false)
    private String formalUser;

}
