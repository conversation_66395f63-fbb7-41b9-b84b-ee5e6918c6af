package org.jeecg.modules.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.user_front.entity.InzUserTrialLog;

import java.util.List;

/**
 * @Description: 用户试用记录Service
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IInzUserTrialLogService extends IService<InzUserTrialLog> {

    /**
     * 根据用户ID查询试用记录
     * @param userId 用户ID
     * @return 试用记录列表
     */
    List<InzUserTrialLog> selectByUserId(String userId);

    /**
     * 计算用户剩余试用天数
     * @param userId 用户ID
     * @return 剩余试用天数
     */
    Integer calculateRemainingDays(String userId);

    /**
     * 添加试用天数记录
     * @param userId 用户ID
     * @param operatorId 操作者ID
     * @param days 增加的天数
     * @param content 操作描述
     * @param educationSeriesId 教育系列ID
     * @return 是否成功
     */
    boolean addTrialDays(String userId, String operatorId, Integer days, String content, String educationSeriesId);

    /**
     * 减少试用天数记录
     * @param userId 用户ID
     * @param operatorId 操作者ID
     * @param days 减少的天数
     * @param content 操作描述
     * @return 是否成功
     */
    boolean reduceTrialDays(String userId, String operatorId, Integer days, String content);

    /**
     * 获取用户已使用的总试用天数
     * @param userId 用户ID
     * @return 已使用的总天数
     */
    Integer getTotalUsedDays(String userId);

    /**
     * 获取用户最新的试用记录
     * @param userId 用户ID
     * @return 最新的试用记录
     */
    InzUserTrialLog getLatestRecord(String userId);

    /**
     * 根据操作者ID查询试用记录
     * @param operatorId 操作者ID
     * @return 试用记录列表
     */
    List<InzUserTrialLog> selectByOperatorId(String operatorId);

    /**
     * 统计指定时间范围内的试用记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 试用记录列表
     */
    List<InzUserTrialLog> selectByTimeRange(String startTime, String endTime);

    /**
     * 检查用户是否有试用记录
     * @param userId 用户ID
     * @return 是否有试用记录
     */
    boolean hasTrialHistory(String userId);

    /**
     * 获取用户试用统计信息
     * @param userId 用户ID
     * @return 统计信息
     */
    TrialStatistics getTrialStatistics(String userId);

    /**
     * 试用统计信息DTO
     */
    class TrialStatistics {
        private Integer totalUsedDays;      // 总使用天数
        private Integer remainingDays;      // 剩余天数
        private Integer recordCount;        // 记录总数
        private String lastOperationTime;  // 最后操作时间
        
        // getters and setters
        public Integer getTotalUsedDays() { return totalUsedDays; }
        public void setTotalUsedDays(Integer totalUsedDays) { this.totalUsedDays = totalUsedDays; }
        
        public Integer getRemainingDays() { return remainingDays; }
        public void setRemainingDays(Integer remainingDays) { this.remainingDays = remainingDays; }
        
        public Integer getRecordCount() { return recordCount; }
        public void setRecordCount(Integer recordCount) { this.recordCount = recordCount; }
        
        public String getLastOperationTime() { return lastOperationTime; }
        public void setLastOperationTime(String lastOperationTime) { this.lastOperationTime = lastOperationTime; }
    }
}
