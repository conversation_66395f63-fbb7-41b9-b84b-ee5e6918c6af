package org.jeecg.modules.user_front.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.entity.InzUserTrialLog;
import org.jeecg.modules.user_front.service.IInzUserFrontService;
import org.jeecg.modules.user_front.service.IInzUserTrialLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 全年权限退单管理Controller
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "后台管理 - 全年权限退单管理")
@RestController
@RequestMapping("/user_front/refund")
@Slf4j
public class RefundController extends JeecgController<InzUserFront, IInzUserFrontService> {

    @Autowired
    private IInzUserFrontService inzUserFrontService;

    @Autowired
    private IInzUserTrialLogService inzUserTrialLogService;

    /**
     * 全年权限退单
     */
    @AutoLog(value = "全年权限退单")
    @ApiOperation(value = "全年权限退单", notes = "代理商为用户办理全年权限退单，8天内100%返还金币，第二次退单不返还")
    @PostMapping(value = "/refundAnnualPermission")
    public Result<String> refundAnnualPermission(@RequestBody RefundRequest refundRequest, HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.info("全年权限退单申请 - 操作者: {}, 目标用户: {}, 原因: {}",
                    operatorId, refundRequest.getUserId(), refundRequest.getRefundReason());

            // 1. 验证操作者权限
            InzUserFront operator = inzUserFrontService.getByBackendUserId(operatorId);
            if (operator == null) {
                return Result.error("操作者信息不存在");
            }

            // 验证操作者是否为代理商
            String operatorRole = operator.getRole();
            if (!"channel".equals(operatorRole) && !"city_partner".equals(operatorRole) &&
                    !"province".equals(operatorRole) && !"area_partner".equals(operatorRole) && !"chuang".equals(operatorRole)) {
                return Result.error("没有退单权限，仅代理商可以操作退单");
            }

            // 2. 验证目标用户
            InzUserFront user = inzUserFrontService.getById(refundRequest.getUserId());
            if (user == null) {
                return Result.error("目标用户不存在");
            }

            // 验证管辖范围
            if (!isUserInManagementScope(operator.getId(), user.getId())) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // 3. 验证用户状态 - 检查是否为正式用户
            if (!"1".equals(user.getFormalUser())) {
                return Result.error("该用户不是正式用户，无法退单");
            }

            // 4. 查找用户的全年权限开通记录
            AnnualPermissionRecord permissionRecord = findLatestAnnualPermission(user.getId());
            if (permissionRecord == null) {
                return Result.error("未找到该用户的全年权限开通记录");
            }

            // 5. 验证退单时间限制（8天内）
            if (!isWithinRefundPeriod(permissionRecord.getCreateTime())) {
                return Result.error("超过退单时间限制，仅支持开通后8天内退单");
            }

            // 6. 检查是否为第二次退单
            boolean isSecondRefund = hasRefundHistory(user.getId());

            // 7. 执行退单操作
            RefundResult result = processRefund(user, permissionRecord, operatorId, refundRequest.getRefundReason(), isSecondRefund);

            if (result.isSuccess()) {
                log.info("全年权限退单成功 - 用户: {}, 操作者: {}, 金币返还: {}",
                        user.getId(), operatorId, result.getRefundAmount());

                String message = isSecondRefund ?
                        "退单成功，由于是第二次退单，金币不予返还" :
                        String.format("退单成功，已返还%d金币", result.getRefundAmount());

                return Result.OK(message);
            } else {
                return Result.error("退单失败：" + result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("全年权限退单失败", e);
            return Result.error("退单操作失败：" + e.getMessage());
        }
    }

    /**
     * 查询用户退单历史
     */
    @AutoLog(value = "查询用户退单历史")
    @ApiOperation(value = "查询用户退单历史", notes = "查询指定用户的退单历史记录")
    @GetMapping(value = "/getRefundHistory")
    public Result<List<RefundHistoryVO>> getRefundHistory(
            @ApiParam(value = "用户ID", required = true) @RequestParam("userId") String userId,
            HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.debug("查询用户退单历史 - 操作者: {}, 目标用户: {}", operatorId, userId);

            // 验证权限
            InzUserFront operator = inzUserFrontService.getByBackendUserId(operatorId);
            if (operator == null) {
                return Result.error("操作者信息不存在");
            }

            InzUserFront user = inzUserFrontService.getById(userId);
            if (user == null) {
                return Result.error("目标用户不存在");
            }

            if (!isUserInManagementScope(operator.getId(), user.getId())) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // 查询退单历史
            List<RefundHistoryVO> refundHistory = getRefundHistoryList(userId);
            return Result.OK(refundHistory);

        } catch (Exception e) {
            log.error("查询用户退单历史失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 检查退单资格
     */
    @AutoLog(value = "检查退单资格")
    @ApiOperation(value = "检查退单资格", notes = "检查用户是否符合退单条件")
    @GetMapping(value = "/checkRefundEligibility")
    public Result<RefundEligibilityVO> checkRefundEligibility(
            @ApiParam(value = "用户ID", required = true) @RequestParam("userId") String userId,
            HttpServletRequest req) {
        try {
            String operatorId = CommonUtils.getUserIdByToken();
            log.debug("检查退单资格 - 操作者: {}, 目标用户: {}", operatorId, userId);

            // 验证权限
            InzUserFront operator = inzUserFrontService.getByBackendUserId(operatorId);
            if (operator == null) {
                return Result.error("操作者信息不存在");
            }

            InzUserFront user = inzUserFrontService.getById(userId);
            if (user == null) {
                return Result.error("目标用户不存在");
            }

            if (!isUserInManagementScope(operator.getId(), user.getId())) {
                return Result.error("该用户不在您的管辖范围内");
            }

            // 检查退单资格
            RefundEligibilityVO eligibility = new RefundEligibilityVO();
            eligibility.setUserId(userId);
            eligibility.setUserName(user.getRealName());
            eligibility.setUserRole(user.getRole());

            // 检查是否为正式用户
            boolean isFormalUser = user.getFormalUser().equals("1");
            eligibility.setIsFormalUser(isFormalUser);

            if (!isFormalUser) {
                eligibility.setEligible(false);
                eligibility.setReason("用户不是正式用户，无法退单");
                return Result.OK(eligibility);
            }

            // 查找权限记录
            AnnualPermissionRecord permissionRecord = findLatestAnnualPermission(userId);
            if (permissionRecord == null) {
                eligibility.setEligible(false);
                eligibility.setReason("未找到全年权限开通记录");
                return Result.OK(eligibility);
            }

            // 检查时间限制
            boolean withinPeriod = isWithinRefundPeriod(permissionRecord.getCreateTime());
            eligibility.setWithinRefundPeriod(withinPeriod);
            eligibility.setPermissionCreateTime(permissionRecord.getCreateTime());

            if (!withinPeriod) {
                eligibility.setEligible(false);
                eligibility.setReason("超过退单时间限制（8天内）");
                return Result.OK(eligibility);
            }

            // 检查退单历史
            boolean hasHistory = hasRefundHistory(userId);
            eligibility.setHasRefundHistory(hasHistory);
            eligibility.setIsSecondRefund(hasHistory);

            // 计算退款金币
            int refundAmount = hasHistory ? 0 : permissionRecord.getGoldenBeanCost();
            eligibility.setRefundAmount(refundAmount);

            eligibility.setEligible(true);
            eligibility.setReason(hasHistory ? "符合退单条件，但因为是第二次退单，金币不予返还" : "符合退单条件，可以100%返还金币");

            return Result.OK(eligibility);

        } catch (Exception e) {
            log.error("检查退单资格失败", e);
            return Result.error("检查失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否在退单期限内（8天内）
     */
    private boolean isWithinRefundPeriod(Date createTime) {
        if (createTime == null) {
            return false;
        }

        long diffInMillis = System.currentTimeMillis() - createTime.getTime();
        long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);

        return diffInDays <= 8;
    }

    /**
     * 检查用户是否有退单历史
     * 基于试用记录判断：如果用户有试用记录，说明之前开通过权限，可能存在退单情况
     */
    private boolean hasRefundHistory(String userId) {
        try {
            // 1. 检查用户是否有试用记录
            boolean hasTrialHistory = inzUserTrialLogService.hasTrialHistory(userId);

            // 2. 如果有试用记录，进一步检查是否有"refund"来源的记录
            if (hasTrialHistory) {
                List<InzUserTrialLog> logs = inzUserTrialLogService.selectByUserId(userId);
                long refundCount = logs.stream()
                    .filter(log -> "refund".equals(log.getSourceType()))
                    .count();

                log.debug("检查用户退单历史 - 用户: {}, 有试用记录: {}, 退单记录数: {}",
                         userId, hasTrialHistory, refundCount);

                // 如果有退单来源的记录，说明是第二次或以上的退单
                return refundCount > 0;
            }

            return false;
        } catch (Exception e) {
            log.error("检查用户退单历史失败 - 用户: {}", userId, e);
            return false;
        }
    }

    /**
     * 查找用户最新的全年权限记录
     * 基于用户的正式用户状态和试用记录来判断
     */
    private AnnualPermissionRecord findLatestAnnualPermission(String userId) {
        try {
            log.debug("查找用户最新全年权限记录 - 用户: {}", userId);

            // 1. 检查用户是否为正式用户
            InzUserFront user = inzUserFrontService.getById(userId);
            if (user == null || !"1".equals(user.getFormalUser())) {
                log.warn("用户不是正式用户，无全年权限记录 - 用户: {}", userId);
                return null;
            }

            // 2. 基于用户成为正式用户的时间创建权限记录
            AnnualPermissionRecord record = new AnnualPermissionRecord();
            record.setId("annual_" + userId);
            record.setUserId(userId);
            record.setGrantType("annual");
            record.setGoldenBeanCost(100); // 假设全年权限花费100金币

            // 3. 使用用户的更新时间作为开通时间（成为正式用户的时间）
            Date permissionTime = user.getUpdateTime() != null ? user.getUpdateTime() : user.getCreateTime();
            record.setCreateTime(permissionTime);

            log.debug("找到用户全年权限记录 - 用户: {}, 开通时间: {}", userId, permissionTime);
            return record;
        } catch (Exception e) {
            log.error("查找用户全年权限记录失败 - 用户: {}", userId, e);
            return null;
        }
    }

    /**
     * 执行退单处理
     */
    private RefundResult processRefund(InzUserFront user, AnnualPermissionRecord permissionRecord,
                                       String operatorId, String refundReason, boolean isSecondRefund) {
        try {
            RefundResult result = new RefundResult();

            // 1. 更新用户状态：从正式用户变回普通用户
            user.setFormalUser("0");  // 设置为非正式用户
            inzUserFrontService.updateById(user);
            log.info("用户状态已更新 - 用户: {}, 从正式用户变更为普通用户", user.getId());

            // 2. 计算退款金币
            int refundAmount = 0;
            if (!isSecondRefund) {
                refundAmount = permissionRecord.getGoldenBeanCost(); // 100%返还

                // 3. 返还金币给操作者（代理商）
                InzUserFront operator = inzUserFrontService.getByBackendUserId(operatorId);
                if (operator != null) {
                    int currentGoldenBean = operator.getGoldenBean() != null ? operator.getGoldenBean() : 0;
                    operator.setGoldenBean(currentGoldenBean + refundAmount);
                    inzUserFrontService.updateById(operator);
                    log.info("金币已返还 - 操作者: {}, 返还金币: {}, 当前金币: {}",
                            operatorId, refundAmount, operator.getGoldenBean());
                }
            }

            // 4. 创建退单试用记录
            createRefundTrialLog(user.getId(), operatorId, refundReason, isSecondRefund);

            // 5. 记录退单日志
            createRefundLog(user.getId(), operatorId, permissionRecord.getId(), refundReason, refundAmount, isSecondRefund);

            // 6. 更新权限记录状态
            updatePermissionRecordStatus(permissionRecord.getId(), "refunded");

            result.setSuccess(true);
            result.setRefundAmount(refundAmount);
            return result;

        } catch (Exception e) {
            log.error("执行退单处理失败", e);
            RefundResult result = new RefundResult();
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            return result;
        }
    }

    /**
     * 创建退单试用记录
     * 在试用记录表中记录退单操作，用于后续判断退单历史
     */
    private void createRefundTrialLog(String userId, String operatorId, String refundReason, boolean isSecondRefund) {
        try {
            String content = String.format("全年权限退单 - %s%s",
                                         refundReason,
                                         isSecondRefund ? "（第二次退单）" : "（首次退单）");

            // 创建退单来源的试用记录，天数为0（仅用于标记退单历史）
            boolean success = inzUserTrialLogService.addTrialDays(
                userId,
                operatorId,
                0,           // 退单不增加试用天数
                content,
                null         // 退单不关联特定教育系列
            );

            if (success) {
                // 更新记录的来源类型为退单
                List<InzUserTrialLog> logs = inzUserTrialLogService.selectByUserId(userId);
                if (!logs.isEmpty()) {
                    InzUserTrialLog latestLog = logs.get(0); // 最新的记录
                    latestLog.setSourceType("refund");
                    inzUserTrialLogService.updateById(latestLog);
                }

                log.info("创建退单试用记录成功 - 用户: {}, 操作者: {}, 是否二次退单: {}",
                        userId, operatorId, isSecondRefund);
            }
        } catch (Exception e) {
            log.error("创建退单试用记录失败 - 用户: {}, 操作者: {}", userId, operatorId, e);
        }
    }

    /**
     * 创建退单记录
     */
    private void createRefundLog(String userId, String operatorId, String permissionRecordId,
                                 String refundReason, int refundAmount, boolean isSecondRefund) {
        try {
            // TODO: 插入退单记录表
            // INSERT INTO inz_refund_log (user_id, operator_id, permission_record_id, refund_reason, refund_amount, is_second_refund, create_time)
            log.info("创建退单记录 - 用户: {}, 操作者: {}, 退款金币: {}, 是否二次退单: {}",
                    userId, operatorId, refundAmount, isSecondRefund);
        } catch (Exception e) {
            log.error("创建退单记录失败", e);
        }
    }

    /**
     * 更新权限记录状态
     */
    private void updatePermissionRecordStatus(String recordId, String status) {
        try {
            // TODO: 更新权限记录状态
            // UPDATE inz_permission_grant_log SET status = ? WHERE id = ?
            log.info("更新权限记录状态 - 记录ID: {}, 状态: {}", recordId, status);
        } catch (Exception e) {
            log.error("更新权限记录状态失败", e);
        }
    }

    /**
     * 获取退单历史列表
     * 基于试用记录表中来源类型为"refund"的记录
     */
    private List<RefundHistoryVO> getRefundHistoryList(String userId) {
        try {
            log.debug("获取退单历史列表 - 用户: {}", userId);

            // 查询用户的试用记录，筛选退单相关的记录
            List<InzUserTrialLog> trialLogs = inzUserTrialLogService.selectByUserId(userId);

            List<RefundHistoryVO> refundHistory = new ArrayList<>();

            for (InzUserTrialLog log : trialLogs) {
                if ("refund".equals(log.getSourceType())) {
                    RefundHistoryVO historyVO = new RefundHistoryVO();
                    historyVO.setId(log.getId());
                    historyVO.setUserId(log.getUserId());
                    historyVO.setOperatorId(log.getOperatorId());

                    // 获取操作者姓名
                    try {
                        InzUserFront operator = inzUserFrontService.getByBackendUserId(log.getOperatorId());
                        historyVO.setOperatorName(operator != null ? operator.getRealName() : "未知操作者");
                    } catch (Exception e) {
                        historyVO.setOperatorName("未知操作者");
                    }

                    historyVO.setRefundReason(log.getContent());
                    historyVO.setRefundAmount(0); // 从试用记录无法直接获取退款金币，需要从其他地方获取
                    historyVO.setIsSecondRefund(log.getContent().contains("第二次退单"));
                    historyVO.setCreateTime(log.getCreateTime());

                    refundHistory.add(historyVO);
                }
            }

            log.debug("获取退单历史列表成功 - 用户: {}, 记录数: {}", userId, refundHistory.size());
            return refundHistory;
        } catch (Exception e) {
            log.error("获取退单历史列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查用户是否在管辖范围内
     */
    private boolean isUserInManagementScope(String agentId, String userId) {
        try {
            InzUserFront user = inzUserFrontService.getById(userId);
            if (user == null) {
                return false;
            }

            // 检查直接上级关系
            if (agentId.equals(user.getParentId())) {
                return true;
            }

            // 检查间接上级关系（递归查找）
            String currentParentId = user.getParentId();
            int maxDepth = 10; // 防止无限递归
            int depth = 0;

            while (currentParentId != null && depth < maxDepth) {
                if (agentId.equals(currentParentId)) {
                    return true;
                }

                InzUserFront parent = inzUserFrontService.getById(currentParentId);
                if (parent == null) {
                    break;
                }

                currentParentId = parent.getParentId();
                depth++;
            }

            return false;
        } catch (Exception e) {
            log.error("检查用户管辖范围失败 - 代理商: {}, 用户: {}", agentId, userId, e);
            return false;
        }
    }

    // DTO类定义
    public static class RefundRequest {
        private String userId;
        private String refundReason;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getRefundReason() {
            return refundReason;
        }

        public void setRefundReason(String refundReason) {
            this.refundReason = refundReason;
        }
    }

    public static class AnnualPermissionRecord {
        private String id;
        private String userId;
        private String grantType;
        private Integer goldenBeanCost;
        private Date createTime;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getGrantType() {
            return grantType;
        }

        public void setGrantType(String grantType) {
            this.grantType = grantType;
        }

        public Integer getGoldenBeanCost() {
            return goldenBeanCost;
        }

        public void setGoldenBeanCost(Integer goldenBeanCost) {
            this.goldenBeanCost = goldenBeanCost;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }
    }

    public static class RefundResult {
        private boolean success;
        private int refundAmount;
        private String errorMessage;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public int getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(int refundAmount) {
            this.refundAmount = refundAmount;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }

    public static class RefundHistoryVO {
        private String id;
        private String userId;
        private String operatorId;
        private String operatorName;
        private String refundReason;
        private Integer refundAmount;
        private Boolean isSecondRefund;
        private Date createTime;

        // getters and setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getOperatorId() {
            return operatorId;
        }

        public void setOperatorId(String operatorId) {
            this.operatorId = operatorId;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public void setOperatorName(String operatorName) {
            this.operatorName = operatorName;
        }

        public String getRefundReason() {
            return refundReason;
        }

        public void setRefundReason(String refundReason) {
            this.refundReason = refundReason;
        }

        public Integer getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(Integer refundAmount) {
            this.refundAmount = refundAmount;
        }

        public Boolean getIsSecondRefund() {
            return isSecondRefund;
        }

        public void setIsSecondRefund(Boolean isSecondRefund) {
            this.isSecondRefund = isSecondRefund;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }
    }

    public static class RefundEligibilityVO {
        private String userId;
        private String userName;
        private String userRole;
        private Boolean isFormalUser;  // 修改：使用formalUser字段
        private Boolean eligible;
        private String reason;
        private Boolean withinRefundPeriod;
        private Date permissionCreateTime;
        private Boolean hasRefundHistory;
        private Boolean isSecondRefund;
        private Integer refundAmount;

        // getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }

        public String getUserRole() { return userRole; }
        public void setUserRole(String userRole) { this.userRole = userRole; }

        public Boolean getIsFormalUser() { return isFormalUser; }
        public void setIsFormalUser(Boolean isFormalUser) { this.isFormalUser = isFormalUser; }

        public Boolean getEligible() { return eligible; }
        public void setEligible(Boolean eligible) { this.eligible = eligible; }

        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }

        public Boolean getWithinRefundPeriod() { return withinRefundPeriod; }
        public void setWithinRefundPeriod(Boolean withinRefundPeriod) { this.withinRefundPeriod = withinRefundPeriod; }

        public Date getPermissionCreateTime() { return permissionCreateTime; }
        public void setPermissionCreateTime(Date permissionCreateTime) { this.permissionCreateTime = permissionCreateTime; }

        public Boolean getHasRefundHistory() { return hasRefundHistory; }
        public void setHasRefundHistory(Boolean hasRefundHistory) { this.hasRefundHistory = hasRefundHistory; }

        public Boolean getIsSecondRefund() { return isSecondRefund; }
        public void setIsSecondRefund(Boolean isSecondRefund) { this.isSecondRefund = isSecondRefund; }

        public Integer getRefundAmount() { return refundAmount; }
        public void setRefundAmount(Integer refundAmount) { this.refundAmount = refundAmount; }
    }
}
