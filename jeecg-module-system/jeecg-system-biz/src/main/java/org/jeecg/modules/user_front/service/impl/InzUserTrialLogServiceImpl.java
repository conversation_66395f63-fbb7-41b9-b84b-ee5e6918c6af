package org.jeecg.modules.user_front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.user_front.entity.InzUserTrialLog;
import org.jeecg.modules.user_front.mapper.InzUserTrialLogMapper;
import org.jeecg.modules.user_front.service.IInzUserTrialLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Description: 用户试用记录Service实现
 * @Author: Alex (工程师)
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzUserTrialLogServiceImpl extends ServiceImpl<InzUserTrialLogMapper, InzUserTrialLog> implements IInzUserTrialLogService {

    private static final Integer MAX_TRIAL_DAYS = 8; // 最大试用天数

    @Override
    public List<InzUserTrialLog> selectByUserId(String userId) {
        return baseMapper.selectByUserId(userId);
    }

    @Override
    public Integer calculateRemainingDays(String userId) {
        try {
            // 统计用户已使用的试用天数
            Integer usedDays = getTotalUsedDays(userId);
            
            // 计算剩余天数
            Integer remainingDays = Math.max(0, MAX_TRIAL_DAYS - usedDays);
            
            log.debug("计算用户剩余试用天数 - 用户: {}, 已使用: {}天, 剩余: {}天", userId, usedDays, remainingDays);
            return remainingDays;
        } catch (Exception e) {
            log.error("计算用户剩余试用天数失败 - 用户: {}", userId, e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addTrialDays(String userId, String operatorId, Integer days, String content, String educationSeriesId) {
        try {
            // 1. 获取当前剩余天数
            Integer beforeDays = calculateRemainingDays(userId);
            Integer afterDays = beforeDays + days;

            // 2. 创建试用记录
            InzUserTrialLog trialLog = new InzUserTrialLog();
            trialLog.setUserId(userId);
            trialLog.setOperatorId(operatorId);
            trialLog.setContent(content);
            trialLog.setTrialDays(days);
            trialLog.setType(1); // 1表示增加
            trialLog.setBeforeDays(beforeDays);
            trialLog.setAfterDays(afterDays);
            trialLog.setEducationSeriesId(educationSeriesId);
            trialLog.setSourceType("manual");
            trialLog.setCreateTime(new Date());

            boolean success = this.save(trialLog);
            
            if (success) {
                log.info("试用天数增加成功 - 用户: {}, 增加: {}天, 操作者: {}", userId, days, operatorId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("添加试用天数失败 - 用户: {}, 天数: {}", userId, days, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reduceTrialDays(String userId, String operatorId, Integer days, String content) {
        try {
            // 1. 获取当前剩余天数
            Integer beforeDays = calculateRemainingDays(userId);
            Integer afterDays = Math.max(0, beforeDays - days);

            // 2. 创建试用记录
            InzUserTrialLog trialLog = new InzUserTrialLog();
            trialLog.setUserId(userId);
            trialLog.setOperatorId(operatorId);
            trialLog.setContent(content);
            trialLog.setTrialDays(-days); // 负数表示减少
            trialLog.setType(0); // 0表示减少
            trialLog.setBeforeDays(beforeDays);
            trialLog.setAfterDays(afterDays);
            trialLog.setSourceType("manual");
            trialLog.setCreateTime(new Date());

            boolean success = this.save(trialLog);
            
            if (success) {
                log.info("试用天数减少成功 - 用户: {}, 减少: {}天, 操作者: {}", userId, days, operatorId);
            }
            
            return success;
        } catch (Exception e) {
            log.error("减少试用天数失败 - 用户: {}, 天数: {}", userId, days, e);
            return false;
        }
    }

    @Override
    public Integer getTotalUsedDays(String userId) {
        try {
            Integer totalDays = baseMapper.getTotalUsedDays(userId);
            log.debug("统计用户已使用试用天数 - 用户: {}, 总天数: {}", userId, totalDays);
            return totalDays != null ? totalDays : 0;
        } catch (Exception e) {
            log.error("统计用户已使用试用天数失败 - 用户: {}", userId, e);
            return 0;
        }
    }

    @Override
    public InzUserTrialLog getLatestRecord(String userId) {
        return baseMapper.getLatestRecord(userId);
    }

    @Override
    public List<InzUserTrialLog> selectByOperatorId(String operatorId) {
        return baseMapper.selectByOperatorId(operatorId);
    }

    @Override
    public List<InzUserTrialLog> selectByTimeRange(String startTime, String endTime) {
        return baseMapper.selectByTimeRange(startTime, endTime);
    }

    @Override
    public boolean hasTrialHistory(String userId) {
        QueryWrapper<InzUserTrialLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public TrialStatistics getTrialStatistics(String userId) {
        try {
            TrialStatistics statistics = new TrialStatistics();
            
            // 统计已使用天数
            Integer totalUsedDays = getTotalUsedDays(userId);
            statistics.setTotalUsedDays(totalUsedDays);
            
            // 计算剩余天数
            Integer remainingDays = calculateRemainingDays(userId);
            statistics.setRemainingDays(remainingDays);
            
            // 统计记录数量
            QueryWrapper<InzUserTrialLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            long recordCount = this.count(queryWrapper);
            statistics.setRecordCount((int) recordCount);
            
            // 获取最后操作时间
            InzUserTrialLog latestRecord = getLatestRecord(userId);
            if (latestRecord != null && latestRecord.getCreateTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                statistics.setLastOperationTime(sdf.format(latestRecord.getCreateTime()));
            }
            
            return statistics;
        } catch (Exception e) {
            log.error("获取用户试用统计信息失败 - 用户: {}", userId, e);
            return new TrialStatistics();
        }
    }
}
